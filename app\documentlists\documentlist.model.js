const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        name: { type: DataTypes.STRING, allowNull: false },
        slug: { type: DataTypes.STRING, allowNull: false },
        size: { type: DataTypes.STRING, allowNull: false },
        status: { type: DataTypes.STRING, allowNull: false },
        filetype: { type: DataTypes.STRING, allowNull: false },
        is_mandatory: { type: DataTypes.STRING, allowNull: false },
        additionalFieldName: {type: DataTypes.STRING, allowNull: true},
        certificate_status: {type: DataTypes.STRING, allowNull: true},
        description: {type: DataTypes.STRING, allowNull: true}
    };

    const options = {
        defaultScope: {
            // exclude hash by default
        },
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("documentlists", attributes, options);
}
