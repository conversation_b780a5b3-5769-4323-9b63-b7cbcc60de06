const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    vinNumber: { type: DataTypes.STRING, allowNull: false },
    inspectionDate: { type: DataTypes.STRING, allowNull: false },
    employeeNumber: { type: DataTypes.INTEGER, allowNull: false },
    inspectorName: { type: DataTypes.STRING, allowNull: true },
    shift: { type: DataTypes.STRING, allowNull: true },
    model: { type: DataTypes.INTEGER, allowNull: true },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("inspections", attributes, options);
}
