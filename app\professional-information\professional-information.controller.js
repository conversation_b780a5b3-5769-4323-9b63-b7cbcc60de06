const express = require("express");
const axios = require("axios");
const config = require("../../config.json");
const router = express.Router();

const BASE_URL = "https://apihspsbx.abdm.gov.in/v4/int/apis/v1/masters";
const FACILITY_BASE_URL =
  "https://apihspsbx.abdm.gov.in/v4/hpr/search/council/facilities";
const MINISTRY_BASE_URL =
  "https://apihspsbx.abdm.gov.in/v4/hfr/master/userMaster";
let BEARER_TOKEN = `eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bDiR6RL_0zYh8oEEYnLT-rVs8Ut1jZOrlpq94r74uM443WJwbk7g5sr3DD6pQLaTXlC0VgYaUFWzqAqmTIXUiOf_vkL9G4AZJO9iZkjrCZ57Yh8F_LFgtWd2G5OUoiofOYcixdBIfdWsNzq2L9rHyutRjx-r3xNNe2E8f8e3KfygbwXi3G5Xb2a9Nv0fgsA9MEwOluqZGndsYZqw5kccCaZBQnPY5s5o8X_dOIctKsDwnFKXX9vKrCggZA2cTI10F5MqIYgPeOtO0ZbAiRISHHQbnB3XO2SjmA1qqjj1I7hT2qZAX3HWn0H57gJZ1nPRM-P7a0kVb6coUuZtUl5iBQ`;

// async function getAccessToken() {
//   const sessionResponse = await axios.post(
//     "https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions",
//     {
//       clientId: config.clientId,
//       clientSecret: config.clientSecret,
//       grantType: config.grantType,
//     },
//     {
//       headers: {
//         "REQUEST-ID": "5ec2f2bc-2ed1-49c8-b9bc-e9adf3786868",
//         TIMESTAMP: "2025-01-22T11:52:05.547Z",
//         "X-CM-ID": "sbx",
//         "Content-Type": "application/json",
//       },
//     }
//   );

//   BEARER_TOKEN = sessionResponse.data.accessToken;
// }

async function getAccessToken() {
  const sessionResponse = await axios.post(
    "https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions",
    {
      clientId: config.clientId,
      clientSecret: config.clientSecret,
      grantType: config.grantType,
    },
    {
      headers: {
        "REQUEST-ID": "5ec2f2bc-2ed1-49c8-b9bc-e9adf3786868",
        TIMESTAMP: "2025-01-22T11:52:05.547Z",
        "X-CM-ID": "sbx",
        "Content-Type": "application/json",
      },
    }
  );
  return sessionResponse.data.accessToken;
}

// Get Spoken Languages
router.get("/languages", async (req, res) => {
  const apiUrl = `${BASE_URL}/languages`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token on 422
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message:
          err.message ||
          "Failed to fetch spoken languages. Please try again later.",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to fetch spoken languages",
    });
  }
});

// Get All States
router.get("/states", async (req, res) => {
  const apiUrl = `${BASE_URL}/states`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh the token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message:
          err.message ||
          "Unable to retrieve states. Please check the API connection.",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to retrieve states",
    });
  }
});

// Get All Districts by State ID
router.get("/district/:stateId", async (req, res) => {
  const { stateId } = req.params;
  const apiUrl = `${BASE_URL}/district/${stateId}`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh the token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: `Could not fetch districts for state ID: ${stateId}. Please try again.`,
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: `No districts found for state ID: ${stateId}`,
    });
  }
});

// Get All Categories (Static Data)
router.get("/categories", (req, res) => {
  try {
    const categories = [
      { id: 1, name: "Doctor" },
      { id: 2, name: "Nurse" },
    ];
    res.status(200).json({ status: true, data: categories });
  } catch (error) {
    res.status(500).json({
      status: false,
      message: "Error retrieving categories. Try again later.",
    });
  }
});

// Get All Medical Councils
router.get("/medical-councils", async (req, res) => {
  const apiUrl = `${BASE_URL}/medical-councils`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    // Handle 422 - Token expired
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed.",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message:
          "Failed to load medical councils. Please check your request and try again.",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "No medical councils found.",
    });
  }
});

// Get All Courses
router.post("/courses", async (req, res) => {
  const reqBody = {
    systemOfMedicine: "Modern Medicine",
    hprType: "doctor",
    qualificationCount: "",
  };

  const apiUrl = `${BASE_URL}/courses`;
  let response;

  try {
    response = await axios.post(apiUrl, reqBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.post(apiUrl, reqBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed.",
          code: retryErr?.response?.status || 500,
          error: retryErr.response ? retryErr.response.data : retryErr.message,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: "Could not retrieve courses. Please check your request.",
        code: err?.response?.status || 500,
        error: err.response ? err.response.data : err.message,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "No courses found.",
    });
  }
});

// Get All Colleges
router.get("/colleges/:stateId", async (req, res) => {
  const { stateId } = req.params;
  const apiUrl = `${BASE_URL}/colleges/${stateId}`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed.",
          code: retryErr?.response?.status || 500,
          error: retryErr.response ? retryErr.response.data : retryErr.message,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: "Failed to retrieve colleges. Please try again later.",
        code: err?.response?.status || 500,
        error: err.response ? err.response.data : err.message,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "No colleges found for the provided state ID.",
    });
  }
});

// Get All Universities
router.get("/universites/:collegeId", async (req, res) => {
  const { collegeId } = req.params;
  const apiUrl = `${BASE_URL}/universites/${collegeId}`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed.",
          code: retryErr?.response?.status || 500,
          error: retryErr.response ? retryErr.response.data : retryErr.message,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: "Error fetching universities. Check your API connection.",
        code: err?.response?.status || 500,
        error: err.response ? err.response.data : err.message,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "No universities found for the provided college ID.",
    });
  }
});

// Get Nature of Works (Static Data)
router.get("/nature-of-works", (req, res) => {
  try {
    const natureOfWorks = [
      "Administrative",
      "Practice",
      "Teaching",
      "Research",
    ];
    res.status(200).json({ status: true, data: natureOfWorks });
  } catch (error) {
    res.status(500).json({
      status: false,
      message: "Error retrieving nature of works. Try again later.",
    });
  }
});

// Get Facilities
router.get("/facilities", async (req, res) => {
  const { facName, state, district } = req.query;

  if (!facName) {
    return res.status(400).json({
      status: false,
      message: "facName parameter is required",
    });
  }

  let apiUrl = `${FACILITY_BASE_URL}?facName=${encodeURIComponent(facName)}`;

  if (state) {
    apiUrl += `&state=${encodeURIComponent(state)}`;
  }

  if (district) {
    apiUrl += `&district=${encodeURIComponent(district)}`;
  }

  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: "Failed to fetch facilities. Please try again later.",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to fetch facilities",
    });
  }
});

// Get Facility by ID
router.get("/facilities/:facilityId", async (req, res) => {
  const { facilityId } = req.params;
  const { category, ministry, facilityType } = req.query;

  let apiUrl = `${FACILITY_BASE_URL}/${facilityId}?`;

  const queryParams = [];
  if (category) queryParams.push(`category=${encodeURIComponent(category)}`);
  if (ministry) queryParams.push(`ministry=${encodeURIComponent(ministry)}`);
  if (facilityType)
    queryParams.push(`facilityType=${encodeURIComponent(facilityType)}`);

  apiUrl += queryParams.join("&");

  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: `Failed to fetch facility with ID: ${facilityId}. Please try again later.`,
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: `No facility found with ID: ${facilityId}`,
    });
  }
});

// Get All Ministry
router.get("/ministries", async (req, res) => {
  const apiUrl = `${MINISTRY_BASE_URL}/getAllMinistry`;
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh token
        response = await axios.get(apiUrl, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: false,
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: false,
        message: "Failed to fetch ministries. Please try again later.",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({ status: true, data: response.data });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to fetch ministries",
    });
  }
});

module.exports = router;
