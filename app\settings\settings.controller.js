﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const settingService = require("./setting.service");

// routes
router.get("/", getAll);
router.post("/", authorize(), create);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), update);
router.delete("/:id", authorize(), _delete);

module.exports = router;

function create(req, res, next) {
	settingService
        .create(req.body)
        .then(() =>
            res.json({ status: true, message: "Record created successfully" })
        )
        .catch(next);
}

function getAll(req, res, next) {
	settingService
		.getAll(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function getById(req, res, next) {
	settingService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		name: Joi.string().required(),
		status: Joi.string().required(),
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	settingService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function _delete(req, res, next) {
	settingService
		.delete(req.params.id)
		.then(() => res.json({ status: true, message: "Record deleted successfully" }))
		.catch(next);
}
