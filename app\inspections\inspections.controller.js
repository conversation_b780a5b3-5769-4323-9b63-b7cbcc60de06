﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const uploadFile = require("../_middleware/upload");
const inspectionService = require("./inspection.service");

// routes
router.get("/", getAll);
router.post("/", createSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.post("/upload", uploadFile.array("files"), doImageUpload);

module.exports = router;

function createSchema(req, res, next) {
  const schema = Joi.object({
    title: Joi.string().required(),
    createdBy: Joi.number().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function doImageUpload(req, res, next) {
  inspectionService
    .doUpload(req)
    .then(() =>
      res.json({ status: true, message: "File Uploaded Successfully!" })
    )
    .catch(next);
}

function create(req, res, next) {
  inspectionService
    .create(req.body)
    .then(() =>
      res.json({ status: true, message: "Record created successfully" })
    )
    .catch(next);
}

function getAll(req, res, next) {
  inspectionService
    .getAll(req.query)
    .then((records) => {
      // return Promise.all(records.map(async element => {
      //     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
      //     return element;
      // })).then(() => {
      //     res.json(records);
      //     next();
      // });

      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  inspectionService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    title: Joi.string().required(),
    updatedBy: Joi.number().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  inspectionService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  inspectionService
    .delete(req.params.id)
    .then(() => res.json({ message: "User deleted successfully" }))
    .catch(next);
}

function activateFile(req, res, next) {
  inspectionService
    .activateFile(req.body)
    .then(() => res.json({ message: "File Activated successfully" }))
    .catch(next);
}

function deactivateFile(req, res, next) {
  inspectionService
    .deactivateFile(req.body)
    .then(() => res.json({ message: "File De-activated successfully" }))
    .catch(next);
}

function activeTeamList(req, res, next) {
  inspectionService
    .getActiveTeamList(req.query.callerfile_id)
    .then((list) => {
      res.json(list);
    })
    .catch(next);
}
