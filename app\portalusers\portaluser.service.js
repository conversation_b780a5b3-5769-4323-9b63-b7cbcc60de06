﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");
const utils = require("../_helpers/util.js");
const emailTemplates = require("../_helpers/templates/templates");
const {
  TYPE_MOBILE,
  ROLE_TELE_CALLER,
  CEO_ROLE,
  DIRECTOR_ROLE,
  ADMIN_ROLE,
  TEAM_LEADER_ROLE,
} = require("../constants");
const { Op } = require("sequelize");
const { fn, col } = db.PortalUser.sequelize;

db.PortalUser.belongsTo(db.Role, {
  as: "roleInfo",
  through: "roles",
  foreignKey: "role_id",
  otherKey: "role_id",
});

module.exports = {
  authenticate,
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  validateEmail,
  requestPasswordReset,
  doPasswordReset,
  resetpassword
};

async function authenticate({ email, password }) {
  console.log("email", email);
  const user = await db.PortalUser.scope("withHash").findOne({
    where: { email },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!user) throw "User not found in System";
  if (!(await bcrypt.compare(password, user.password))) {
    throw "Email or Password is incorrect";
    return;
  }
  if (user && user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
    return;
  }
  // authentication successful
  const token = jwt.sign({ sub: user.id }, config.secret, {
    expiresIn: "7d",
  });

  return {
    userInfo: { ...omitHash(user.get()) },
    token,
  };
}

async function getAll(params) {
  return await db.PortalUser.findAll({
    attributes: {
      include: [
        [fn("concat", col("firstname"), " ", col("lastname")), "fullName"],
      ],
    },
    include: [
      {
        model: db.Role,
        as: "roleInfo",
        attributes: ["id", "name"],
      },
    ],
  });
}

async function getById(id) {
  return await getUser(id);
}

async function create(params) {
  // validate
  if (await db.PortalUser.findOne({ where: { email: params.email } })) {
    throw 'email "' + params.email + '" is already taken';
    return;
  }

  // hash password
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // save user
  const record = await db.PortalUser.create(params);
  return record;
}

async function fetchUserIds(usersList, managerIds) {
  let newList = [];
  for (const managerId of managerIds) {
    const listOfUsers = await db.PortalUser.findAll({
      where: { managerId },
      attributes: ["id"],
      raw: true,
    });
    newList = [...newList, ...listOfUsers.map((node) => node.id)];
  }
  if (newList && newList.length > 0) {
    return await fetchUserIds(usersList, newList);
  }
  usersList = [...usersList, ...newList];
  return usersList;
}

async function updateUsersStatus(id, status) {
  let listOfUsersId = [];
  const listOfUsers = await fetchUserIds(listOfUsersId, [id]);
  await db.PortalUser.update(
    { status },
    {
      where: {
        id: {
          [Op.in]: listOfUsers,
        },
      },
    }
  );
}

async function update(id, params) {
  const user = await getUser(id);

  // validate
  const emailChanged = params.email && user.email !== params.email;
  if (
    emailChanged &&
    (await db.PortalUser.findOne({ where: { email: params.email } }))
  ) {
    throw 'email "' + params.email + '" is already taken';
  }

  // hash password if it was entered
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // copy params to user and save
  Object.assign(user, params);
  await user.save();

  return omitHash(user.get());
}

async function _delete(id) {
  const user = await getUser(id);
  await user.destroy();
}

// helper functions

async function getUser(id) {
  const user = await db.PortalUser.findByPk(id);
  if (!user) throw "User not found";
  return user;
}

function omitHash(user) {
  const { password, ...userWithoutHash } = user;
  return userWithoutHash;
}

async function validateEmail(email) {
  const user = await db.PortalUser.findOne({ where: { email } });
  if (!user) {
    throw "User not found";
    return;
  }
  if (user && user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
    return;
  }
  return user;
}

async function requestPasswordReset(params) {
  const clientURL = config.clientUrl;
  let record = await validateEmail(params?.email);
  if (record) {
    let random = (Math.random() + 1).toString(36).substring(7);
    // let random = Math.floor(100000 + Math.random() * 900000);
    token = await bcrypt.hash(random, 10);
    // token = random;
    record.token = token;
    await record.save();
    const link = `${clientURL}/reset-password?email=${record.email}&token=${token}&id=${record.id}`;
    const fullName = `${record.firstName} ${record.lastName}`;
    const emailBody = emailTemplates.requestPasswordTemplate(fullName, link);
    const emailStatus = await utils.sendEmail(
      `${record.email}`,
      "Password Reset Request",
      emailBody
    );
    if (!emailStatus) {
      throw "We were unable to send the email. Please retry again.";
    } else {
      return true;
    }
  }
}

async function doPasswordReset(params) {
  if (!params.email) {
    throw "Please enter email address";
  }
  if (!params.token) {
    throw "Looks like something is wrong with your request. Please try again.";
  }
  if (!params.password) {
    throw "Please enter password";
  }
  if (!params.confirmPassword) {
    throw "Please enter confirm password";
  }
  let record = await db.PortalUser.findOne({
    where: {
      email: params.email,
      token: params.token,
    },
  });
  if (!record) {
    throw "User not found";
    return;
  }
  if (record) {
    const password = await bcrypt.hash(params.password, 10);
    record.password = password;
    record.token = null;
    await record.save();
    // const link = `${clientURL}/passwordReset?token=${resetToken}&id=${user._id}`;
  }
}
async function resetpassword(params) {
  if (!params.email) {
    throw "Please enter email address";
  }

  if (!params.currentPassword) {
    throw "Please enter old password";
  }
  if (!params.newPassword) {
    throw "Please enter new password";
  }
  let record = await db.PortalUser.findOne({
    where: {
      email: params.email,
    },
  });
  if (!record) {
    throw "User not found";
    return;
  }
  if (record) {
    const password = await bcrypt.hash(params.newPassword, 10);
    record.password = password;
    await record.save();
    // const link = `${clientURL}/passwordReset?token=${resetToken}&id=${user._id}`;
  }
}

async function fetchAllDirectors() {
  const directorRole = await db.Role.findOne({
    where: {
      name: DIRECTOR_ROLE,
    },
  });
  const users = await db.PortalUser.findAll({
    where: {
      roleId: directorRole.id,
      status: "Active",
    },
    attributes: {
      include: [
        [fn("concat", col("firstname"), " ", col("lastname")), "fullName"],
      ],
    },
  });
  return users;
}
