﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");

module.exports = {
    create,
	getById,
	update,
	delete: _delete,
    getAll
};

async function create(params) {
	// validate
	const record = await db.Experience.create(params);
    if(record) {
        // copy params to user and save
        const profileRecord = await db.Profile.findOne({where: {
            id: params?.user_id
        }})
        
        Object.assign(profileRecord, {experiencesUploaded: 25});
        await profileRecord.save();
    }
	return record;
    // return true;
	
}

async function getAll(params) {
	return await db.Experience.findAll({
        where: { user_id: params?.user_id},
    }
);
}


async function getById(id) {
	return await getQualification(id);
}


async function update(id, params) {
	const record = await getSingleRecord(id);
	// copy params to user and save
	Object.assign(record, params);
	await record.save();

	return record.get();
}

async function _delete(id) {
	const record = await getQualification(id);
	await record.destroy();
}

async function getSingleRecord(id) {
    const record = await db.Experience.findOne({
        where: {
            id: id,
        },
    });
    if (!record) throw "Invalid ID";
    return record;
}

// helper functions

async function getQualification(id) {
	const qualification = await db.Experience.findByPk(id);
	if (!qualification) throw "User not found";
	return qualification;
}


