const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        mode_of_recruitment: { type: DataTypes.STRING, allowNull: false },
        examination_name: { type: DataTypes.STRING, allowNull: false },
        advertisement_number: { type: DataTypes.STRING, allowNull: false },
        advertisement_date: { type: DataTypes.DATE, allowNull: false },
        start_date: { type: DataTypes.DATE, allowNull: false },
        fee_last_date: { type: DataTypes.DATE, allowNull: false },
        form_submission_last_date: { type: DataTypes.DATE, allowNull: false },
        createdBy: { type: DataTypes.INTEGER, allowNull: false },
        updatedBy: { type: DataTypes.INTEGER, allowNull: true },
        status: { type: DataTypes.STRING, allowNull: false },
        advertisement_filename: { type: DataTypes.STRING, allowNull: true },
        user_instructions_filename: { type: DataTypes.STRING, allowNull: true },
    };

    const options = {
        defaultScope: {
            // exclude hash by default
        },
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("advertisements", attributes, options);
}
