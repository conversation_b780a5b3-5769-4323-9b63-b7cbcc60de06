﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");
const utils = require("../_helpers/util.js");
const emailTemplates = require("../_helpers/templates/templates");
const { Op } = require("sequelize");
const { fn, col } = db.User.sequelize;
const moment = require("moment");

const crypto = require("crypto");

module.exports = {
  authenticate,
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  validateEmail,
  requestPasswordReset,
  doPasswordReset,
  doUpload,
  doVerifyQR,
  resetpassword,
};

async function authenticate({ email, password, type }) {
  const user = await db.User.scope("withHash").findOne({
    where: { email },
  });
  if (!user) throw "User not found in System";
  if (!(await bcrypt.compare(password, user.password))) {
    throw "Email or Password is incorrect";
    return;
  }
  if (user && user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
    return;
  }
  // authentication successful
  const token = jwt.sign({ sub: user.id }, config.secret, {
    expiresIn: "7d",
  });

  return {
    userInfo: { ...omitHash(user.get()) },
    token,
  };
}

async function getAll(params) {
  let where = {
    // profile_completed:"yes"
  };
  // where.profile_status = "pending";
  // where.payment_status = "completed";
  if (!!params?.certificate_status) {
    where.certificate_status = params.certificate_status;
  }
  if (!!params?.application_status) {
    where.profile_status = params.application_status;
  }

  return await db.User.findAll({
    where,
    attributes: {
      include: [
        [fn("concat", col("firstname"), " ", col("lastname")), "fullName"],
      ],
    },
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getUser(id);
}

async function create(params) {
  // validate
  if (await db.User.findOne({ where: { email: params.email } })) {
    throw 'email "' + params.email + '" is already taken';
    return;
  }

  if (
    await db.User.findOne({
      where: { mobile_number: params.mobile_number },
    })
  ) {
    throw 'mobile_number "' + params.mobile_number + '" is already taken';
    return;
  }

  // hash password
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // if(params.certificate_status === "Old Certificate") {
  //     if(!params?.central_registration_number) {
  //         throw "Central Registration Number is mandatory";
  //     }
  //     if(await db.User.findOne({
  //         where: { central_registration_number: params.central_registration_number },
  //     })) {
  //         throw 'Central Registration Number "' + params.central_registration_number + '" is already taken';
  //         return;
  //     }
  // }

  if (!!params?.date_of_birth) {
    params.date_of_birth = moment(params.date_of_birth).format("YYYY-MM-DD");
  }
  if (!!params?.state_registration_date) {
    params.state_registration_date = moment(
      params.state_registration_date
    ).format("YYYY-MM-DD");
  } else {
    params.state_registration_date = null;
  }
  // save user
  const record = await db.User.create(params);
  if (record) {
    await insertDocumentsForNew(record.id, params?.certificate_status);
  }
  return record;
}

async function insertDocumentsForNew(id, certificateType) {
  let certificate_status = "New";
  if (certificateType === "Old Certificate") {
    certificate_status = "Old";
  }
  const documentlist = await db.DocumentList.findAll({
    where: {
      certificate_status,
    },
  });
  let array = [];
  documentlist.forEach((dl) => {
    array.push({
      user_id: id,
      documentName: dl.name,
      fileSize: dl.size,
      is_mandatory: dl.is_mandatory,
      fileType: dl.filetype,
      certificate_status: dl.certificate_status,
      description: dl.description,
    });
  });
  if (array.length > 0) {
    return await db.Document.bulkCreate(array);
  }
  return;
}

async function fetchUserIds(usersList, managerIds) {
  let newList = [];
  for (const managerId of managerIds) {
    const listOfUsers = await db.User.findAll({
      where: { managerId },
      attributes: ["id"],
      raw: true,
    });
    newList = [...newList, ...listOfUsers.map((node) => node.id)];
  }
  if (newList && newList.length > 0) {
    return await fetchUserIds(usersList, newList);
  }
  usersList = [...usersList, ...newList];
  return usersList;
}

async function update(id, params) {
  const user = await getUser(id);

  // validate
  const emailChanged = params.email && user.email !== params.email;
  if (
    emailChanged &&
    (await db.User.findOne({ where: { email: params.email } }))
  ) {
    throw 'email "' + params.email + '" is already taken';
  }

  // hash password if it was entered
  if (params.password) {
    params.password = await bcrypt.hash(params.password, 10);
  }

  // Generate Central Registration Number
  if (!!params?.date_of_birth) {
    params.date_of_birth = moment(params.date_of_birth).format("YYYY-MM-DD");
  }
  if (!!params?.state_registration_date) {
    params.state_registration_date = moment(
      params.state_registration_date
    ).format("YYYY-MM-DD");
  } else {
    params.state_registration_date = null;
  }
  if (
    params?.profile_status === "Approved" &&
    (!params?.central_registration_number ||
      params?.central_registration_number === "null")
  ) {
    const regNumber = await db.User.findOne({
      where: {
        central_registration_number: {
          [Op.ne]: null,
        },
      },
      order: [["central_registration_number", "DESC"]],
    });
    if (
      !regNumber?.central_registration_number ||
      regNumber?.central_registration_number === "null"
    ) {
      const settings = await db.Setting.findOne({ where: { id: 1 } });
      params.central_registration_number =
        settings.dataValues?.cr_number_start_from;
      // params.approved_date = moment().format("YYYY-MM-DD");
    } else {
      if (Number(regNumber?.central_registration_number) < 10) {
        params.central_registration_number =
          "0" + (Number(regNumber?.central_registration_number) + 1);
      } else {
        params.central_registration_number =
          Number(regNumber?.central_registration_number) + 1;
      }

      // params.approved_date = moment().format("YYYY-MM-DD");
    }

    params.central_registration_number_pre = moment().format("YYYY") + "NRB";
  }
  if (params?.profile_status === "Approved" && !params.approved_date) {
    params.approved_date = moment().format("YYYY-MM-DD");
  }
  Object.assign(user, params);
  await user.save();
  if (params?.profile_status === "Approved") {
    if (user.certificate_status === "Old Certificate") {
      const emailBody = emailTemplates.emailForCCRYNRegistered(
        params?.profile_status,
        params?.central_registration_number,
        params?.remarks
      );
      const emailStatus = await utils.sendEmail(
        `${params.email}`,
        `Regarding Registration under Naturopathy Registration Board`,
        emailBody
      );
      if (!emailStatus) {
        throw "We were unable to send the email. Please retry again.";
      } else {
        return true;
      }
    }
    if (user.certificate_status === "New Certificate") {
      const emailBody = emailTemplates.emailForNewRegistered(
        params?.profile_status,
        params?.central_registration_number,
        params?.remarks
      );
      const emailStatus = await utils.sendEmail(
        `${params.email}`,
        `Regarding Registration under Naturopathy Registration Board`,
        emailBody
      );
      if (!emailStatus) {
        throw "We were unable to send the email. Please retry again.";
      } else {
        return true;
      }
    }
  }
  if (params?.profile_status === "Rejected") {
    const emailBody = emailTemplates.emailForRejectedApplication(
      user?.firstname,
      params?.central_registration_number,
      params?.remarks
    );
    const emailStatus = await utils.sendEmail(
      `${params.email}`,
      `Regarding Registration under Naturopathy Registration Board`,
      emailBody
    );
    if (!emailStatus) {
      throw "We were unable to send the email. Please retry again.";
    } else {
      return true;
    }
  }

  return omitHash(user.get());

  // copy params to user and save
}

async function _delete(id) {
  const user = await getUser(id);
  await user.destroy();
}

// helper functions

async function getUser(id) {
  const user = await db.User.findByPk(id);
  if (!user) throw "User not found";
  return user;
}

function omitHash(user) {
  const { password, ...userWithoutHash } = user;
  return userWithoutHash;
}

async function validateEmail(email) {
  const user = await db.User.findOne({ where: { email } });
  if (!user) {
    throw "User not found";
    return;
  }
  if (user && user.status === "Inactive") {
    throw "Your account is Inactive. Please contact administrator for more information";
    return;
  }
  return user;
}

async function requestPasswordReset(params) {
  const clientURL = config.clientUrl;
  let record = await validateEmail(params?.email);
  if (record) {
    let random = (Math.random() + 1).toString(36).substring(7);
    // let random = Math.floor(100000 + Math.random() * 900000);
    token = await bcrypt.hash(random, 10);
    // token = random;
    record.token = token;
    await record.save();
    const link = `${clientURL}/reset-password?email=${record.email}&token=${token}&id=${record.id}`;
    const fullName = `${record.firstName} ${record.lastName}`;
    const emailBody = emailTemplates.requestPasswordTemplate(fullName, link);
    const emailStatus = await utils.sendEmail(
      `${record.email}`,
      "Password Reset Request",
      emailBody
    );
    if (!emailStatus) {
      throw "We were unable to send the email. Please retry again.";
    } else {
      return true;
    }
  }
}

async function doPasswordReset(params) {
  if (!params.email) {
    throw "Please enter email address";
  }
  if (!params.token) {
    throw "Looks like something is wrong with your request. Please try again.";
  }
  if (!params.password) {
    throw "Please enter password";
  }
  if (!params.confirmPassword) {
    throw "Please enter confirm password";
  }
  let record = await db.User.findOne({
    where: {
      email: params.email,
      token: params.token,
    },
  });
  if (!record) {
    throw "User not found";
    return;
  }
  if (record) {
    const password = await bcrypt.hash(params.password, 10);
    record.password = password;
    record.token = null;
    await record.save();
    // const link = `${clientURL}/passwordReset?token=${resetToken}&id=${user._id}`;
  }
}

async function doVerifyQR(params) {
  if (!params?.qrCode) {
    throw "Invalid Data";
  }
  let split = params?.qrCode?.split("-");
  let user = await db.User.findOne({
    where: {
      central_registration_number_pre: split[0],
      central_registration_number: split[1],
    },

    attributes: [
      "firstname",
      "middlename",
      "lastname",
      "date_of_birth",
      "approved_date",
      "central_registration_number",
      "central_registration_number_pre",
      "permanent_address",
      "qualification",
      "id",
    ],
  });

  if (!user) throw "Record not found";
  let returnedData = { ...user?.dataValues };
  if (user) {
    const documents = await db.Document.findOne({
      where: {
        user_id: user.id,
        documentName: {
          [Op.like]: "%Photograph with white background%",
        },
      },
    });
    if (documents) {
      returnedData.documentInfo = documents;
    }
  }
  return returnedData;
}

async function doUpload(req) {
  try {
    let params = {};

    params = {
      firstname: req.body.firstname,
      middlename: req?.body?.middlename,
      lastname: req?.body?.lastname,
      aadhar_number: req?.body?.aadhar_number,
      mobile_number: req?.body?.mobile_number,
      email: req?.body?.email,
      password: req?.body?.password,
      date_of_birth: req?.body?.date_of_birth,
      street1: req?.body?.street1,
      street2: req?.body?.street2,
      city: req?.body?.city,
      state: req?.body?.state,
      postcode: req?.body?.postcode,
      category_name: req?.body?.category_name,
      status: req?.body?.status,
    };
    if (req?.files && req.files.length > 0) {
      req?.files.forEach((element) => {
        if (element.originalname.startsWith("ADDRESSPROOF")) {
          params.address_filename = element.filename;
        }
        if (element.originalname.startsWith("DOB")) {
          params.date_of_birth_filename = element.filename;
        }
        if (element.originalname.startsWith("AADHARPROOF")) {
          params.aadhar_number_filename = element.filename;
        }
      });
    }
    console.log("params", params);
    let result = null;
    if (!req.body?.id) {
      result = create(params);
    } else {
      result = update(req.body.id, params);
    }
    return result;

    //   if (result.id && fileList && fileList.length > 0) {
    //     await bulkCreateImages(
    //       fileList,
    //       result.id,
    //       params.vinNumber
    //     );
    //   }
  } catch (error) {
    console.log(error);
    throw "Could not upload the file: ";
    res.status(500).send({
      message: "Could not upload the file: ",
    });
  }
}
async function resetpassword(params) {
  if (!params.email) {
    throw "Please enter email address";
  }

  if (!params.currentPassword) {
    throw "Please enter old password";
  }
  if (!params.newPassword) {
    throw "Please enter new password";
  }
  let record = await db.User.findOne({
    where: {
      email: params.email,
    },
  });
  if (!record) {
    throw "User not found";
    return;
  }
  if (record) {
    const password = await bcrypt.hash(params.newPassword, 10);
    record.password = password;
    await record.save();
    // const link = `${clientURL}/passwordReset?token=${resetToken}&id=${user._id}`;
  }
}
