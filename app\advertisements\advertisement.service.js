﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
	getAll,
	getById,
	create,
	update,
	delete: _delete,
    doUpload
};

async function getAll() {
	return await db.Advertisement.findAll({where: {
        status: "Active",
    }});
}

async function getById(id) {
	return await getSingleRecord(id);
}

async function create(params) {
	// validate
	if (await db.Advertisement.findOne({ where: { advertisement_number: params.advertisement_number} })) {
        throw 'Record "' + params.advertisement_number + '" is already taken';
        return;
    }

	const record = await db.Advertisement.create(params);
	return record;
	
}

async function doUpload(req) {
    try {
        let params = {};
      
      params = {
        mode_of_recruitment: req.body.mode_of_recruitment,
        examination_name: req?.body?.examination_name,
        advertisement_number: req?.body?.advertisement_number,
        advertisement_date: req?.body?.advertisement_date,
        start_date: req?.body?.start_date,
        fee_last_date: req?.body?.fee_last_date,
        form_submission_last_date: req?.body?.form_submission_last_date,
        status: req?.body?.status,
        createdBy: req?.body?.createdBy,
        updatedBy: req?.body?.updatedBy,
      };
      if(req?.files && req.files.length > 0) {
        req?.files.forEach(element => {
            if(element.originalname.startsWith("INSTRUCTIONS")) {
                params.user_instructions_filename = element.filename; 
            }
            if(element.originalname.startsWith("ADVERTISEMENT")) {
                params.advertisement_filename = element.filename; 
            }
        });
      }
      let result = null;
      if(!req.body?.id) {
        result = await db.Advertisement.create(params);
      } else {
        result = update(req.body.id, params);
      }
      return result;
      
    //   if (result.id && fileList && fileList.length > 0) {
    //     await bulkCreateImages(
    //       fileList,
    //       result.id,
    //       params.vinNumber
    //     );
    //   }
    } catch (error) {
      
        console.log(error);
        throw "Could not upload the file: ";
        res.status(500).send({
          message: "Could not upload the file: ",
        });
    }
  }
  

async function update(id, params) {
	const record = await getSingleRecord(id);
	// validate
	
	const recordChanged = params.advertisement_number && record.advertisement_number !== params.advertisement_number;
	if (
		recordChanged &&
		(await db.Advertisement.findOne({ where: { advertisement_number: params.advertisement_number } }))
	) {
		throw 'Record "' + params.advertisement_number + '" is already taken';
	}

	// copy params to user and save
	Object.assign(record, params);
	await record.save();

	return record.get();
}

async function _delete(id) {
	const record = await getSingleRecord(id);
	await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
	const record = await db.Advertisement.findByPk(id);
	if (!record) throw "Record not found";
	return record;
}
