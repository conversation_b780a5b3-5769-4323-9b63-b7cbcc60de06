const express = require("express");
const router = express.Router();
const Joi = require("joi");
const axios = require("axios");
const config = require("../../config.json");

const aadhaarService = require("./aadhar-verification.service");

let BEARER_TOKEN = `eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Q53BT-2TI_arHeTBKo58U0ewzOCsdPgVKWX8e0EXvXYyWAl8szItFS6dYgO2KHgbnJchXQp8ym30BWnSBrUIcNFy3Aj_NZbjfePmhKyuDWD6UhYxDCC6I7lmtJEFuL2-3pC9JTbSDP3ZxtithKfmh1QA-1Gbsp29jX_7fCt4klMIyPUPbA4MgZ-vzuBDcUzZ1d8ci2ztHh9g-O2SZhIQarilc9GpXoNv4KYtRMQprnJqtahJHUyisMCxaBIgkzpNrm4a3ZpKP0UMWW9KXq2Ox4ZScOOVWfFXKHHOm8F78QCY0-QqlvNkPF8zZYfoRGqmPvw4q3Vut9hjWLqZXe9FBQ`;

// routes
router.post("/generateOtp", encryptAadhaarNumber);
router.post("/verifyOtp", verifyOtp);
router.get("/getpublicKey", fetchPublicKey);
router.post("/checkHPIDExist", checkHPIDExist);
router.post("/checkMobileNumberSameAsAAdhar", checkMobileNumberSameAsAAdhar);
router.post("/generateMobileOtp", generateMobileOtp);
router.post("/verifyMobileOtp", verifyMobileOtp);
router.post("/getuserNameSuggestions", getuserNameSuggestions);
router.post("/createHPRID", createHPRID);
router.post("/getauthPassword/:userId", getauthPassword);
router.post("/userAuthorizedToken/:userId", userAuthorizedToken);
router.post("/registerProfessional", registerProfessional);

module.exports = router;

//public key generation
async function getAccessToken() {
  const sessionResponse = await axios.post(
    "https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions",
    {
      clientId: config.clientId,
      clientSecret: config.clientSecret,
      grantType: config.grantType,
    },
    {
      headers: {
        "REQUEST-ID": "5ec2f2bc-2ed1-49c8-b9bc-e9adf3786868",
        TIMESTAMP: "2025-01-22T11:52:05.547Z",
        "X-CM-ID": "sbx",
        "Content-Type": "application/json",
      },
    }
  );
  return sessionResponse.data.accessToken;
}

async function getPublicKey() {
  const apiUrl = "https://apihspsbx.abdm.gov.in/v4/int/api/v1/auth/cert";
  let response;

  try {
    response = await axios.get(apiUrl, {
      headers: {
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken(); // Refresh the token
        response = await axios.get(apiUrl, {
          headers: {
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        throw new Error(
          `Retry after refreshing token failed: ${
            retryErr.message || "Unknown error"
          }`
        );
      }
    } else {
      throw new Error(
        `Initial request failed: ${err.message || "Unknown error"}`
      );
    }
  }

  return response?.data;
}

async function fetchPublicKey(req, res) {
  try {
    const publicKey = await getPublicKey();
    res.json(publicKey);
  } catch (error) {
    console.error("Error fetching public key:", error);
    res.status(500).json({ error: error?.message || "Internal Server Error" });
  }
}

// utility function
async function encryptAadhaarNumber(req, res, next) {
  const { aadharNumber } = req.body;
  const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v2/registration/aadhaar/generateOtp`;

  let response;
  let requestBody;
  try {
    const publicKey = await getPublicKey();

    const encryptedAadhaar = aadhaarService.encryptValue(
      aadharNumber,
      publicKey
    );

    requestBody = {
      aadhaar: encryptedAadhaar,
    };

    response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken();
        response = await axios.post(apiUrl, requestBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: "error",
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: "error",
        message: err.message || "Initial request failed",
        code: err?.response?.status || 500,
      });
    }
  }
  if (response?.data?.txnId) {
    return res.status(200).json({
      status: true,
      message: "OTP sent successfully",
      data: response.data,
    });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to send OTP",
    });
  }
}

async function verifyOtp(req, res, next) {
  const { otp, txnId } = req.body;
  const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v2/registration/aadhaar/verifyOTP`;

  let response;
  let requestBody;

  try {
    const publicKey = await getPublicKey();
    const encryptedOtp = aadhaarService.encryptValue(otp, publicKey);
    console.log(encryptedOtp,"encryptedOtp");
    requestBody = {
      otp: encryptedOtp,
      txnId: txnId,
    };

    response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken();
        response = await axios.post(apiUrl, requestBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: "error",
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: "error",
        message: err.message || "Initial request failed",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data?.txnId) {
    return res.status(200).json({
      status: true,
      message: "OTP verified successfully",
      data: response.data,
    });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to verify OTP",
    });
  }
}

async function checkHPIDExist(req, res, next) {
  const { txnId } = req.body; // txn id from generate OTP API
  const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v1/registration/aadhaar/checkHpIdAccountExist`;

  let response;
  const requestBody = { txnId };

  try {
    response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken();
        response = await axios.post(apiUrl, requestBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        return res.status(500).json({
          status: "error",
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: "error",
        message: err.message || "Initial request failed",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({
      status: true,
      message: "HPID existence check successful",
      data: response.data,
    });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to check HPID existence",
    });
  }
}

async function checkMobileNumberSameAsAAdhar(req, res, next) {
  const { txnId, mobileNumber } = req.body;

  try {
    const publicKey = await getPublicKey();
    const encryptMobileNumber = aadhaarService.encryptValue(
      mobileNumber,
      publicKey
    );

    const requestBody = {
      txnId: txnId,
      mobileNumber: encryptMobileNumber,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v2/registration/aadhaar/demographicAuthViaMobile`;

    let response;

    try {
      response = await axios.post(apiUrl, requestBody, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${BEARER_TOKEN}`,
        },
      });
    } catch (error) {
      if (error.response?.status === 422) {
        try {
          // Refresh token
          BEARER_TOKEN = await getAccessToken();

          // Retry the request
          response = await axios.post(apiUrl, requestBody, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${BEARER_TOKEN}`,
            },
          });
        } catch (retryError) {
          return res.status(500).json({
            status: false,
            message: "Retry after token refresh failed.",
            code: retryError?.response?.status || 500,
            error: retryError.response
              ? retryError.response.data
              : retryError.message,
          });
        }
      } else {
        return res.status(500).json({
          status: false,
          message: error.message || "Error verifying mobile number",
          code: error?.response?.status || 500,
          error: error.response ? error.response.data : error.message,
        });
      }
    }

    if (response?.data && response.data.verified) {
      return res.status(200).json({
        status: true,
        isMobileNumberSame: true,
        message: "Your mobile number is same as Aadhar",
        data: response.data,
      });
    } else {
      return res.status(200).json({
        status: true,
        isMobileNumberSame: false,
        message:
          "Your mobile number in profile does not match the registered Aadhar number.",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: error.message || "Unexpected error during mobile number check",
    });
  }
}

async function generateMobileOtp(req, res, next) {
  const { txnId, mobile } = req.body; //txn id from generate OTP api
  try {
    const requestBody = {
      txnId: txnId,
      mobile: mobile,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v1/registration/aadhaar/generateMobileOTP`;

    const response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });

    if (response.data && response.data.txnId) {
      return res.status(200).json({
        status: true,
        message: "OTP verified successfully",
        data: response.data,
      });
    } else {
      return res.status(400).json({
        status: false,
        message: "Failed to verify OTP",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: error.message || "Something went wrong during verification",
    });
  }
}
async function verifyMobileOtp(req, res, next) {
  const { txnId, otp } = req.body; //txn id from generate OTP api
  try {
    const requestBody = {
      txnId: txnId,
      otp: otp,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v1/registration/aadhaar/verifyMobileOTP`;

    const response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });

    if (response.data && response.data.txnId) {
      return res.status(200).json({
        status: true,
        message: "OTP verified successfully",
        data: response.data,
      });
    } else {
      return res.status(400).json({
        status: false,
        message: "Failed to verify OTP",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: error.message || "Something went wrong during verification",
    });
  }
}
async function getuserNameSuggestions(req, res, next) {
  const { txnId } = req.body;

  try {
    const requestBody = {
      txnId: txnId,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v1/registration/aadhaar/hpid/suggestion`;

    let response;

    try {
      response = await axios.post(apiUrl, requestBody, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${BEARER_TOKEN}`,
        },
      });
    } catch (error) {
      if (error.response?.status === 422) {
        try {
          // Refresh token
          BEARER_TOKEN = await getAccessToken();

          // Retry the request
          response = await axios.post(apiUrl, requestBody, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${BEARER_TOKEN}`,
            },
          });
        } catch (retryError) {
          return res.status(500).json({
            status: false,
            message: "Retry after token refresh failed.",
            code: retryError?.response?.status || 500,
            error: retryError.response
              ? retryError.response.data
              : retryError.message,
          });
        }
      } else {
        return res.status(500).json({
          status: false,
          message:
            error.message ||
            "Something went wrong during username suggestion fetch",
          code: error?.response?.status || 500,
          error: error.response ? error.response.data : error.message,
        });
      }
    }

    if (response?.data) {
      return res.status(200).json({
        status: true,
        message: "User names fetched successfully",
        data: response.data,
      });
    } else {
      return res.status(400).json({
        status: false,
        message: "Failed to fetch usernames",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: error.message || "Unexpected server error",
    });
  }
}

async function createHPRID(req, res, next) {
  const {
    txnId,
    email,
    profilePhoto,
    firstName,
    password,
    district_id,
    state_id,
    hprId,
  } = req.body;

  try {
    const publicKey = await getPublicKey();
    const encryptedPassword = aadhaarService.encryptValue(password, publicKey);
    const encryptEmail = aadhaarService.encryptValue(email, publicKey);

    const requestBody = {
      txnId: txnId,
      email: encryptEmail,
      idType: "hpr_id",
      domainName: "@hpr.abdm",
      firstName: firstName,
      middleName: "",
      lastName: "",
      password: encryptedPassword,
      profilePhoto: profilePhoto,
      hprId: hprId,
      sourceType: "AADHAAR",
      hpCategoryCode: 1,
      hpSubCategoryCode: 220,
      clientId: config.clientId,
      stateCode: state_id,
      districtCode: district_id,
      council: false,
      role: 3,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/v2/registration/aadhaar/createHprIdWithPreVerified`;

    let response;

    try {
      response = await axios.post(apiUrl, requestBody, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${BEARER_TOKEN}`,
        },
      });
    } catch (error) {
      if (error.response?.status === 422) {
        try {
          BEARER_TOKEN = await getAccessToken(); // Refresh the token
          response = await axios.post(apiUrl, requestBody, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${BEARER_TOKEN}`,
            },
          });
        } catch (retryError) {
          return res.status(500).json({
            status: false,
            message: "Retry after token refresh failed.",
            code: retryError?.response?.status || 500,
            error: retryError.response
              ? retryError.response.data
              : retryError.message,
          });
        }
      } else {
        return res.status(500).json({
          status: false,
          message: error.message || "Something went wrong while creating HPID",
          code: error?.response?.status || 500,
          error: error.response ? error.response.data : error.message,
        });
      }
    }

    if (response?.data) {
      return res.status(200).json({
        status: true,
        message: "HPID created successfully",
        data: response.data,
      });
    } else {
      return res.status(400).json({
        status: false,
        message: "Failed to create HPID",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message:
        error.message || "Something went wrong during encryption or key fetch",
    });
  }
}

async function getauthPassword(req, res, next) {
  const { password, hprId } = req.body;

  try {
    const requestBody = {
      idType: "hpr_id",
      domainName: "@hpr.abdm",
      hprId: hprId,
      password: password,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/api/v1/auth/authPassword`;

    let response;

    try {
      response = await axios.post(apiUrl, requestBody, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${BEARER_TOKEN}`,
        },
      });
    } catch (error) {
      if (error.response?.status === 422) {
        try {
          // Refresh token
          BEARER_TOKEN = await getAccessToken();

          // Retry the request
          response = await axios.post(apiUrl, requestBody, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${BEARER_TOKEN}`,
            },
          });
        } catch (retryError) {
          return res.status(500).json({
            status: false,
            message: "Token refresh and retry failed.",
            code: retryError?.response?.status || 500,
            error: retryError.response
              ? retryError.response.data
              : retryError.message,
          });
        }
      } else {
        return res.status(500).json({
          status: false,
          message: error.message || "Error during auth password request",
          code: error?.response?.status || 500,
          error: error.response ? error.response.data : error.message,
        });
      }
    }

    if (response?.data) {
      return res.status(200).json({
        status: true,
        message: "Token generated successfully",
        data: response.data,
      });
    } else {
      return res.status(400).json({
        status: false,
        message: "Failed to generate token",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: error.message || "Unexpected error during verification",
    });
  }
}

async function userAuthorizedToken(req, res, next) {
  const { hpId, txnId } = req.body; //txn id from generate OTP api

  try {
    const requestBody = {
      txnId: txnId,
      hpId: hpId,
    };

    const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/api/v2/auth/login/userAuthorizedToken`;

    const response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });

    if (response.data) {
      return res.status(200).json({
        status: true,
        message: "Token generated successfully ",
        data: response.data,
      });
    } else {
      return res.status(400).json({
        status: false,
        message: "Failed to generate token",
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: "error",
      message: error.message || "Something went wrong during verification",
    });
  }
}

async function registerProfessional(req, res, next) {
  const {
    token,
    healthProfessionalType,
    officialMobile,
    firstName,
    nationality,
    languagesSpoken,
    address,
    country,
    state,
    district,
    pincode,
    category,
    registeredWithCouncil,
    registrationNumber,
    isPermanentOrRenewable,
    renewableDueDate,
    nameOfDegreeOrDiplomaObtained,
    university,
    college,
    yearOfAwardingDegreeDiploma,
    currentlyWorking,
    purposeOfWork,
    chooseWorkStatus,
    reasonForNotWorking,
    profilePhoto,

    registrationDate,
    degreeCertificateUrl,
    registrationCertificateUrl,
  } = req.body;
  const apiUrl = `https://apihspsbx.abdm.gov.in/v4/int/apis/v1/doctors/register-professional-new`;
  const requestBody = {
    hprToken: token,
    practitioner: {
      healthProfessionalType: healthProfessionalType,
      apiClientId: "",
      profilePhoto: profilePhoto,
      officialMobileCode: "",
      officialMobile: officialMobile,
      officialMobileStatus: "",
      officialEmail: "",
      officialEmailStatus: "",
      visibleProfilePicture: "",
      profileVisibleToPublic: "1",
      personalInformation: {
        salutation: 1,
        firstName: firstName,
        middleName: "",
        lastName: "",
        nationality: nationality,
        fatherName: "",
        motherName: "",
        spouseName: "",
        placeOfBirthState: "",
        district: "",
        subDistrict: "",
        city: "",
        languagesSpoken: languagesSpoken,
      },
      addressAsPerKYC: "",
      communicationAddress: {
        isCommunicationAddressAsPerKYC: "false",
        address: address,
        name: firstName,
        country: country,
        state: state,
        district: district,
        subDistrict: "",
        city: "",
        pincode: pincode,
      },
      contactInformation: {
        publicMobileNumber: "",
        publicMobileNumberCode: "",
        publicMobileNumberStatus: "",
        landLineNumber: "",
        landLineNumberCode: "",
        publicEmail: "",
        publicEmailStatus: "",
      },
      registrationAcademic: {
        category: healthProfessionalType == "doctor" ? "1" : "2",
        registrationData: [
          {
            registeredWithCouncil: registeredWithCouncil,
            registrationNumber: registrationNumber,
            registrationDate: registrationDate,
            registrationCertificate: {
              fileType: "pdf",
              data: registrationCertificateUrl,
            },
            isPermanentOrRenewable: isPermanentOrRenewable,
            renewableDueDate: renewableDueDate,
            categoryId: healthProfessionalType == "doctor" ? "1" : "2",
            isNameDifferentInCertificate: "",
            proofOfNameChangeCertificate: "",
            qualifications: [
              {
                nameOfDegreeOrDiplomaObtained: nameOfDegreeOrDiplomaObtained,
                country: country,
                state: state,
                college: college,
                university: university,
                yearOfAwardingDegreeDiploma: yearOfAwardingDegreeDiploma,
                monthOfAwardingDegreeDiploma: "",
                degreeCertificate: {
                  fileType: "pdf",
                  data: degreeCertificateUrl,
                },
                isNameDifferentInCertificate: "false",
                proofOfNameChangeCertificate: "",
              },
            ],
          },
        ],
      },
      specialities: null,
      currentWorkDetails: {
        currentlyWorking: currentlyWorking,
        purposeOfWork: "Practice",
        chooseWorkStatus: chooseWorkStatus,
        reasonForNotWorking: reasonForNotWorking,
        certificateAttachment: "",
        facilityDeclarationData: {
          facilityId: "",
          facilityName: "",
          facilityAddress: "",
          facilityPincode: "",
          state: "",
          district: "",
          facilityType: "",
          facilityDepartment: "",
          facilityDesignation: "",
        },
      },
    },
  };
  console.log("----------------------------------");
  console.dir(requestBody, { depth: null, colors: true });
  console.log("----------------------------------");

  let response;

  try {
    response = await axios.post(apiUrl, requestBody, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${BEARER_TOKEN}`,
      },
    });
  } catch (err) {
    if (err.response?.status === 422) {
      try {
        BEARER_TOKEN = await getAccessToken();
        console.log(BEARER_TOKEN,"BEARER_TOKEN");
        response = await axios.post(apiUrl, requestBody, {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${BEARER_TOKEN}`,
          },
        });
      } catch (retryErr) {
        console.log(retryErr,"retryErr");
        return res.status(500).json({
          status: "error",
          message: "Retry after refreshing token failed",
          code: retryErr?.response?.status || 500,
        });
      }
    } else {
      return res.status(500).json({
        status: "error",
        message: err.message || "Initial request failed",
        code: err?.response?.status || 500,
      });
    }
  }

  if (response?.data) {
    return res.status(200).json({
      status: true,
      message: "Profile submitted successfully",
      data: response.data,
    });
  } else {
    return res.status(400).json({
      status: false,
      message: "Failed to submit profile",
    });
  }
}
