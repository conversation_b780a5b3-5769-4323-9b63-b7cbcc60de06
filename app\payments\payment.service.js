﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const crypto = require("crypto");
const config = require("../../config.json");
const { Op } = require("sequelize");

module.exports = {
	getAll,
	// getById,
	create,
	update,
    updateRefund,
	// delete: _delete,
    verify
};

db.Payment.belongsTo(db.User, {
    through: "payments",
    foreignKey: "user_id",
    otherKey: "userId",
});

async function getAll() {
	return await db.Payment.findAll({
        include: [
            {
                model: db.User,
                attributes: ["id", "firstname", "lastname"],
            },
        ],
        order: [["payment_id", "DESC"]],
    });
}

// async function getById(id) {
// 	return await getSingleRecord(id);
// }

async function create(params) {
	// validate
    try {
        // Razor Pay Integration
        // const instance = new Razorpay({
        //     key_id:config.KEY_ID,
        //     key_secret:config.KEY_SECRET
        // })
        // const options = {
        //     amount: params?.amount*100,
        //     currency:"INR",
        //     receipt: crypto.randomBytes(10).toString("hex")
        // }
        // const record = await instance.orders.create(options);

        // const redirectUrl = `https://test.ccavenue.com/transaction/transaction.do?command=initiateTransaction&merchant_id=${config.MERCHANT_ID}&order_id=${crypto.randomBytes(5)}&amount=${params?.amount}&currency=INR&redirect_url=http://localhost:3000/payment-response`;
        const orderNumber = await db.Payment.findOne({ where: { order_id:{
            [Op.ne]: null
          } }, 
        order: [["order_id", "DESC"]], });
        let newOrderNumber = "";
        if(!orderNumber) {
            newOrderNumber = "111111";
        } else {
            newOrderNumber = Number(orderNumber?.order_id) + 1;
        }
        const object = {
            ...params,
            payment_status: "pending",
            user_id: params.user_id,
            merchant_id:config.merchant_id,
            redirect_url:config.REDIRECT_URL_PROD,
            cancel_url:config.REDIRECT_URL_PROD,
            currency:"INR",
            language:"EN",
            merchant_id:config?.MERCHANT_ID,
            order_id: newOrderNumber
        }
        console.log(object);
        const row = await db.Payment.create(object);
        if(row) {
            if(object?.payment_type === "refund") {
                const paymentObject = await db.Payment.findOne({where: {
                    payment_id: object.original_payment_id
                }});
                return {
                    object,
                    paymentObject
                }
            } else {
                return object;
            }
        }
    } catch(error) {
        console.log(error);
        throw "Internal Server Error";
    }
	// const record = await db.Setting.create(params);
	// return record;
	
}

async function verify(params) {
	// validate
    console.log("params",params);
    try {
        const {  } = params;
		const sign = razorpay_order_id.toString() + "|" + razorpay_payment_id.toString();
        console.log("sign",sign);
		const expectedSign = crypto
			.createHmac("sha256", config.KEY_SECRET)
			.update(sign)
			.digest("hex");

        console.log("razorpay_signature",razorpay_signature);

        console.log("expectedSign",expectedSign);
		if (razorpay_signature === expectedSign) {
            const updatedRecord = await update(params);
            if(updatedRecord.payment_id) {
                const userRecord = await db.User.findOne({where: {id: updatedRecord?.user_id}});
                if(userRecord.id) {
                    const userParams = {
                        payment_id: updatedRecord?.payment_id
                    }
                    Object.assign(userRecord, userParams);
	                await userRecord.save();
                }
            }
            return "Payment verified successfully";
        } else {
            throw "Invalid signature sent!"
        }
    } catch(error) {
        console.log(error);
        throw "Internal Server Error";
    }
	
}

async function update(params) {
	const record = await db.Payment.findOne({where: {
        order_id: params.order_id
    }});
    delete params.order_id;
    let payload = {
        payment_status: params?.order_status?.toLowerCase(),
        all_values: JSON.stringify(params),
        tracking_id:params?.tracking_id,
        payment_mode:params?.payment_mode,
        bank_ref_no:params?.bank_ref_no,
    }
    // spreadObject.payment_status = "success";
	// copy params to user and save
	Object.assign(record, payload);
	await record.save();
    if(payload.payment_status === "success") {
        const userRecord = await db.User.findOne({where: {id: record?.user_id}});
        if(userRecord.id) {
            const userParams = {
                payment_id: record?.payment_id
            }
            Object.assign(userRecord, userParams);
            await userRecord.save();
        }
    }
    return record.get();
	// return spreadObject.get();
}

async function updateRefund(params) {
	const record = await db.Payment.findOne({where: {
        order_id: params.order_id
    }});
    // delete params.order_id;
    // let payload = {
    //     payment_status: params?.order_status?.toLowerCase(),
    //     all_values: JSON.stringify(params),
    //     tracking_id:params?.tracking_id,
    //     payment_mode:params?.payment_mode,
    //     bank_ref_no:params?.bank_ref_no,
    // }
    // spreadObject.payment_status = "success";
	// copy params to user and save
	Object.assign(record, params);
	return await record.save();
    // if(payload.payment_status === "success") {
    //     const userRecord = await db.User.findOne({where: {id: record?.user_id}});
    //     if(userRecord.id) {
    //         const userParams = {
    //             payment_id: record?.payment_id
    //         }
    //         Object.assign(userRecord, userParams);
    //         await userRecord.save();
    //     }
    // }
    // return record.get();
	// return spreadObject.get();
}

// async function _delete(id) {
// 	const record = await getSingleRecord(id);
// 	await record.destroy();
// }

// // helper functions

// async function getSingleRecord(id) {
// 	const record = await db.Setting.findByPk(id);
// 	if (!record) throw "Record not found";
// 	return record;
// }
