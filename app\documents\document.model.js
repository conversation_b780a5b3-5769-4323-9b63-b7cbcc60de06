const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        user_id: { type: DataTypes.INTEGER, allowNull: false },
        documentName: { type: DataTypes.STRING, allowNull: false },
        fileName: { type: DataTypes.STRING, allowNull: false },
        additionalFieldName: { type: DataTypes.STRING, allowNull: true },
        certificate_status: { type: DataTypes.STRING, allowNull: true },
        is_mandatory: { type: DataTypes.STRING, allowNull: true },
        fileSize: { type: DataTypes.STRING, allowNull: true },
        fileType: { type: DataTypes.STRING, allowNull: true },
        description: { type: DataTypes.STRING, allowNull: true },
    };

    const options = {
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("documents", attributes, options);
}
