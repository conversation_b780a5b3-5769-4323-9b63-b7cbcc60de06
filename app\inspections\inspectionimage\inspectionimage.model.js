const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    imageName: { type: DataTypes.STRING, allowNull: false },
    imageUri: { type: DataTypes.STRING, allowNull: true },
    vinNumber: { type: DataTypes.STRING, allowNull: true },
    checkPoint: { type: DataTypes.STRING, allowNull: true },
    inspection_id: { type: DataTypes.INTEGER, allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("inspection_images", attributes, options);
}
