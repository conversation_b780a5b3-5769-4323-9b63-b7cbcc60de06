const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const multer = require("multer");
const uploadFile = require("../_middleware/upload");

const documentService = require("./document.service");


const storage = multer.diskStorage({
	destination: (req, file, cb) => {
        let dir = __basedir + "uploads/documents";
		console.log("dir",dir);
        cb(null, __basedir + "uploads/documents/");
	},
	filename: (req, file, cb) => {
        
		// const fileName = file.originalname.toLowerCase().split(" ").join("-");
		cb(null, file.originalname);
	},
});

// STAND ALONE CONFIG
const uploadConfig = multer({
	storage: storage,
	fileFilter: (req, file, cb) => {
        console.log("file",file);
		if (
			file.mimetype == "application/pdf" ||
                file.mimetype == "image/png" ||
                file.mimetype == "image/jpg" ||
                file.mimetype == "image/jpeg"
		) {
			cb(null, true);
		} else {
			cb(null, false);
			return cb(new Error("Only pdf and images file format allowed!"));
		}
	},
});

// routes
router.get("/",  getAll);
router.get("/:id",  getById);
router.put(
	"/:id",
	uploadConfig.single("file"),
	update
);
// router.post("/", uploadFile.single("files"), create);
router.post("/", uploadConfig.single("file"), create);
router.delete("/:id", authorize(), _delete);

module.exports = router;

function getAll(req, res, next) {
	documentService
		.getAll(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function getById(req, res, next) {
	documentService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}


function create(req, res, next) {
	documentService
		.create(req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		firstName: Joi.string().empty(""),
		lastName: Joi.string().empty(""),
		email: Joi.string().empty(""),
		phoneNumber: Joi.string().required(),
		zipcode: Joi.string().required(),
		city: Joi.string().required(),
		state: Joi.string().required(),
		country: Joi.string().required(),
		address: Joi.string().required(),
		password: Joi.string().min(6).empty(""),
		profileImagePath: Joi.string().allow("",null),
        companyImagePath: Joi.string().allow("",null),
        images: Joi.string().allow("",null),
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	documentService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function _delete(req, res, next) {
	documentService
		.delete(req.params.id)
		.then(() => res.json({ status: true, message: "Record deleted successfully" }))
		.catch(next);
}
