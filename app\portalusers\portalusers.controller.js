﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const testEmail = require("../_helpers/util.js");

const portalUserService = require("./portaluser.service");

// routes
router.post("/authenticate", authenticateSchema, authenticate);
router.post("/", registerSchema, create);
router.get("/", authorize(), getAll);
router.get("/validateEmail", validateEmail);
router.get("/current", authorize(), getCurrent);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), updateSchema, update);
router.delete("/:id", authorize(), _delete);
router.post("/requestPasswordReset", requestPasswordReset);
router.post("/doPasswordReset", doPasswordReset);
//router.post("/doSendEmail", doSendEmail)

module.exports = router;

function authenticateSchema(req, res, next) {
  const schema = Joi.object({
    email: Joi.string().required(),
    password: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

/*function doSendEmail() {
    testEmail();
    // sendEmail("<EMAIL>")
}*/

function authenticate(req, res, next) {
  portalUserService
    .authenticate(req.body)
    .then((user) => res.json({ status: true, result: user }))
    .catch(next);
}

function registerSchema(req, res, next) {
  const schema = Joi.object({
    firstname: Joi.string().required(),
    lastname: Joi.string().required(),
    email: Joi.string().required(),
    password: Joi.string().min(6).required(),
    role_id: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function create(req, res, next) {
  portalUserService
    .create(req.body)
    .then((record) =>
      res.json({
        status: true,
        message: "User created successfully",
        userId: record.id,
      })
    )
    .catch(next);
}

function getAll(req, res, next) {
  portalUserService
    .getAll(req.query)
    .then((users) => res.json(users))
    .catch(next);
}

function getCurrent(req, res, next) {
  res.json(req.user);
}

function getById(req, res, next) {
  portalUserService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    firstname: Joi.string().required(),
    lastname: Joi.string().required(),
    email: Joi.string().required(),
    password: Joi.string().min(6).empty(""),
    role_id: Joi.string().required(),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  portalUserService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  portalUserService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}

function validateEmail(req, res, next) {
  portalUserService
    .validateEmail(req.query.email)
    .then(() => res.json({ status: true }))
    .catch(next);
}

function requestPasswordReset(req, res, next) {
  portalUserService
    .requestPasswordReset(req.body)
    .then(() =>
      res.json({
        status: true,
        message: "Reset password email sent successfully",
      })
    )
    .catch(next);
}

function doPasswordReset(req, res, next) {
  portalUserService
    .doPasswordReset(req.body)
    .then(() =>
      res.json({ status: true, message: "Password changed successfully" })
    )
    .catch(next);
}
