﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const applicationService = require("./application.service");

// routes
router.get("/", authorize(), getAll);
router.get("/reports", authorize(), generateReports);
router.get("/dashboardRecords",  generateDashboardRecords);
router.post("/", authorize(), updateSchema, create);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), updateSchema, update);
router.delete("/:id", authorize(), _delete);

module.exports = router;

function create(req, res, next) {
	applicationService
        .create(req.body)
        .then(() =>
            res.json({ status: true, message: "You have successfully applied for vacancy" })
        )
        .catch(next);
}

function getAll(req, res, next) {
	applicationService
		.getAll(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function generateReports(req, res, next) {
	applicationService
		.generateReports(req.query)
		.then((records) => {
			res.json(records);
		})
		.catch(next);
}

function generateDashboardRecords(req, res, next) {
	applicationService
		.generateDashboardRecords(req.query)
		.then((records) => {
			res.json(records);
		})
		.catch(next);
}

function getById(req, res, next) {
	applicationService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		user_id: Joi.number().required(),
		vacancy_id: Joi.number().required(),
		advertisement_id: Joi.number().required(),
		category_name: Joi.string().required(),
		form_submitted_on: Joi.string().required(),
		applied_on: Joi.string().required(),
		fees_paid_on: Joi.string().allow('',null),
		status: Joi.string().required(),
		remarks: Joi.string().allow('',null),
		updated_by: Joi.number().allow('',null),
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	applicationService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function _delete(req, res, next) {
	applicationService
		.delete(req.params.id)
		.then(() => res.json({ status: true, message: "Record deleted successfully" }))
		.catch(next);
}
