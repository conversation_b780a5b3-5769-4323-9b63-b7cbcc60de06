﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const paymentService = require("./payment.service");
var crypto = require("crypto");
const axios = require("axios");
const config = require("../../config.json");
const qs = require('querystring');

// routes
router.post("/initPaymentRequest", initPaymentRequest);
router.post("/paymentResponseHandler", paymentResponseHandler);
router.get("/", getAll);
router.post("/", authorize(), create);
router.post("/verify", authorize(), verify);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), update);
router.delete("/:id", authorize(), _delete);

module.exports = router;

function create(req, res, next) {
    paymentService
        .create(req.body)
        .then((order) => {
            if(order?.object?.payment_type === "refund") {
                const record = initPaymentRefund(order);
                if(record.status === "success") {
                    res.json({ status: true, message:"Refund Initiated Successfully" });
                } else {
                    res.json({ status: true, message:"Failed to Initiate Refund" });
                }
            } else {
                res.json({ status: true, order });
            }
        })
        .catch(next);
}

async function initPaymentRequest(request, response) {
    var body = "",
        workingKey = config.WORKING_KEY_PROD, //Put in the 32-Bit key shared by CCAvenues.
        accessCode = config.ACCESS_CODE_PROD, //Put in the Access Code shared by CCAvenues.
        encRequest = "",
        formbody = "";

    //Generate Md5 hash for the key and then convert in base64 string
    var md5 = crypto.createHash("md5").update(workingKey).digest();
    var keyBase64 = Buffer.from(md5).toString("base64");

    //Initializing Vector and then convert in base64 string
    var ivBase64 = Buffer.from([
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f,
    ]).toString("base64");

    request.on("data", function (data) {
        body += data;
        encRequest = encrypt(body, keyBase64, ivBase64);
        response.json({
            encRequest,
            access_code: accessCode,
        });
        // formbody = '<form id="nonseamless" method="post" name="redirect" action="https://test.ccavenue.com/transaction/transaction.do?command=initiateTransaction"/> <input type="hidden" id="encRequest" name="encRequest" value="' + encRequest + '"><input type="hidden" name="access_code" id="access_code" value="' + accessCode + '"><script language="javascript">document.redirect.submit();</script></form>';
    });
    return;
    request.on("end", function () {
        response.json(formbody);
        //     response.writeHeader(200, {"Content-Type": "text/html"});
        // response.write(formbody);
        response.end();
    });
}


async function initPaymentRefund(paramObject) {
    var body = "",
        workingKey = config.WORKING_KEY_PROD, //Put in the 32-Bit key shared by CCAvenues.
        accessCode = config.ACCESS_CODE_PROD, //Put in the Access Code shared by CCAvenues.
        encRequest = "",
        formbody = "";

    //Generate Md5 hash for the key and then convert in base64 string
    var md5 = crypto.createHash("md5").update(workingKey).digest();
    var keyBase64 = Buffer.from(md5).toString("base64");

    //Initializing Vector and then convert in base64 string
    var ivBase64 = Buffer.from([
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b,
        0x0c, 0x0d, 0x0e, 0x0f,
    ]).toString("base64");

    const formObject = {
        reference_no:paramObject?.paymentObject?.tracking_id,
        refund_amount:2000,
        refund_ref_no: paramObject?.object?.order_id
    }

    const object = `${paramObject?.paymentObject?.tracking_id}|2000|${paramObject?.object?.order_id}|`;

    const params1 = Object.keys(formObject)
                .map(
                    (key) =>
                        `${encodeURIComponent(key)}=${encodeURIComponent(
                            formObject[key]
                        )}`
                )
                .join("&");
    console.log("oara",params1);

//     const params1 = new URLSearchParams();

// // Append each key-value pair to the URLSearchParams
//     for (const [key, value] of Object.entries(formObject)) {
//         if (Array.isArray(value)) {
//             value.forEach(v => params.append(key, v)); // For arrays, append each value
//         } else {
//             params1.append(key, value);
//         }
//     }


//Collecting payment from the client and Refund Payment to the client

    encRequest = encrypt(object, keyBase64, ivBase64);

    const finalObject = {
        request_type: "STRING",
        access_code: accessCode,
        command:"refundOrder",
        response_type: "JSON",
        enc_request: encRequest,
        version:1.1
    }
    

    const params = Object.keys(finalObject)
    .map(
        (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(
                finalObject[key]
            )}`
    )
    .join("&");

//     const params = new URLSearchParams();

// // Append each key-value pair to the URLSearchParams
//     for (const [key, value] of Object.entries(finalObject)) {
//         if (Array.isArray(value)) {
//             value.forEach(v => params.append(key, v)); // For arrays, append each value
//         } else {
//             params.append(key, value);
//         }
//     }
    // const refundData = qs.stringify(formObject, { arrayFormat: 'brackets' });
    const refundRequest = params;
    console.log("refund",refundRequest);

    const url = `https://api.ccavenue.com/apis/servlet/DoWebTrans?${refundRequest}`;
    console.log(url);
    const response = await axios.post(`${url}`, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    });
    const convertResponseToJSON = qs.parse(response?.data);
    let paymentResponseObject = {...paramObject?.object};
    console.log("convertResponseToJSON",convertResponseToJSON);
    if(convertResponseToJSON?.enc_response.startsWith("You are not allowed")) {
        console.log("inside");
        paymentResponseObject.payment_status = 'failed';
        paymentResponseObject.all_values = response?.data;


    } else {
        const all_values = decrypt(convertResponseToJSON?.enc_response, keyBase64, ivBase64);
        paymentResponseObject.payment_status = 'success';
        paymentResponseObject.all_values = JSON.stringify(all_values);

    }
    console.log("paymentResponseObject",paymentResponseObject);
    const update = await paymentService.updateRefund(paymentResponseObject);
    if(update) {
        return {
            status:"success"
        }
    } else {
        return {
            status:"failed"
        }
    }
    console.log("response",convertResponseToJSON);


    // await paymentService.update()

    // request.on("data", function (data) {
    //     body += data;
    //     encRequest = encrypt(body, keyBase64, ivBase64);
    //     response.json({
    //         encRequest,
    //         access_code: accessCode,
    //     });
    //     // formbody = '<form id="nonseamless" method="post" name="redirect" action="https://test.ccavenue.com/transaction/transaction.do?command=initiateTransaction"/> <input type="hidden" id="encRequest" name="encRequest" value="' + encRequest + '"><input type="hidden" name="access_code" id="access_code" value="' + accessCode + '"><script language="javascript">document.redirect.submit();</script></form>';
    // });
    return;
    request.on("end", function () {
        response.json(formbody);
        //     response.writeHeader(200, {"Content-Type": "text/html"});
        // response.write(formbody);
        response.end();
    });
}


async function paymentResponseHandler(request,response) {
    var ccavEncResponse='',
	ccavResponse='',	
	workingKey = config?.WORKING_KEY_PROD,	//Put in the 32-Bit key shared by CCAvenues.
	ccavPOST = '';
	
    //Generate Md5 hash for the key and then convert in base64 string
    var md5 = crypto.createHash('md5').update(workingKey).digest();
    var keyBase64 = Buffer.from(md5).toString('base64');
    
    //Initializing Vector and then convert in base64 string
    var ivBase64 = Buffer.from([0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d,0x0e, 0x0f]).toString('base64');
        const body = request?.body;
        // request.on('data', function (data) {
	    ccavEncResponse = request?.body;
	    ccavPOST =  ccavEncResponse;
	    var encryption = ccavPOST.encResp;
	    ccavResponse = decrypt(encryption, keyBase64, ivBase64);
        // });
        if(ccavResponse) {
            var newResponse = qs.parse(ccavResponse);
            paymentService
                .update(newResponse)
                .then((user) => {
                    var pData = '';
                    pData = `${newResponse.order_status === "Failure" ||  newResponse.order_status === "Aborted" || newResponse.order_status === "Invalid" ? `<h1 style="color:red">Payment Failed / Aborted</h1>`: `<h1 style="color:green">Payment Successful</h1>`}`;
                    pData = pData + `<a href="https://app.nrb.net.in/#/my-documents/">Go Back to NRB Application</a>`;
                    pData = pData + '<table border=1 cellspacing=2 cellpadding=2><tr><td>'	
                    pData = pData + ccavResponse.replace(/=/gi,'</td><td>')
                    pData = pData.replace(/&/gi,'</td></tr><tr><td>')
                    pData = pData + '</td></tr></table>'
                        htmlcode = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Response Handler</title></head><body><center><font size="4" color="blue"><b>Response Page</b></font><br>'+ pData +'</center><br><center><div><a href="https://app.nrb.net.in/#/my-documents/">Go Back</a></div></center></body></html>';
                        response.writeHeader(200, {"Content-Type": "text/html"});
                    response.write(htmlcode);
                    response.end();
                })
           
        }
        // request.on('end', function () {

        // console.log("ccavResponse",ccavResponse)
            
        // }); 
}


function verify(req, res, next) {
    paymentService
        .verify(req.body)
        .then(() =>
            res.json({ status: true, message: "Payment verified successfully" })
        )
        .catch(next);
}

function getAll(req, res, next) {
    paymentService
        .getAll(req.query)
        .then((records) => {
            // return Promise.all(records.map(async element => {
            //     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
            //     return element;
            // })).then(() => {
            //     res.json(records);
            //     next();
            // });

            res.json(records);
        })
        .catch(next);
}

function getById(req, res, next) {
    paymentService
        .getById(req.params.id)
        .then((user) => res.json(user))
        .catch(next);
}

function updateSchema(req, res, next) {
    const schema = Joi.object({
        name: Joi.string().required(),
        status: Joi.string().required(),
    });
    validateRequest(req, next, schema);
}

function update(req, res, next) {
    paymentService
        .update(req.params.id, req.body)
        .then((user) => res.json(user))
        .catch(next);
}

function _delete(req, res, next) {
    paymentService
        .delete(req.params.id)
        .then(() =>
            res.json({ status: true, message: "Record deleted successfully" })
        )
        .catch(next);
}

function getAlgorithm(keyBase64) {
    var key = Buffer.from(keyBase64, "base64");
    switch (key.length) {
        case 16:
            return "aes-128-cbc";
        case 32:
            return "aes-256-cbc";
    }
    throw new Error("Invalid key length: " + key.length);
}


function encrypt(plainText, keyBase64, ivBase64) {
    const key = Buffer.from(keyBase64, "base64");
    const iv = Buffer.from(ivBase64, "base64");

    const cipher = crypto.createCipheriv(getAlgorithm(keyBase64), key, iv);
    let encrypted = cipher.update(plainText, "utf8", "hex");
    encrypted += cipher.final("hex");
    return encrypted;
}

function decrypt(messagebase64, keyBase64, ivBase64) {
    const key = Buffer.from(keyBase64, "base64");
    const iv = Buffer.from(ivBase64, "base64");

    const decipher = crypto.createDecipheriv(getAlgorithm(keyBase64), key, iv);
    let decrypted = decipher.update(messagebase64, "hex");
    decrypted += decipher.final();
    return decrypted;
}

