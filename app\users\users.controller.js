﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const testEmail = require("../_helpers/util.js");

const userService = require("./user.service");
const uploadFile = require("../_middleware/upload");

// routes

router.get("/qrInfo", verifyQR);
router.post("/authenticate", authenticateSchema, authenticate);
router.post("/", authorize(), registerSchema, create);
router.post("/register", registerSchema, register);
router.get("/", authorize(), getAll);
router.post("/newRegistration", uploadFile.array("userfiles"), doFileUpload);
router.get("/teamleaders", authorize(), getTeamLeaders);
router.get("/directors", authorize(), getDirectors);
router.get("/validateEmail", validateEmail);
router.get("/current", authorize(), getCurrent);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), update);
router.delete("/:id", authorize(), _delete);
router.post("/requestPasswordReset", requestPasswordReset);
router.post("/doPasswordReset", doPasswordReset);
router.post("/resetpassword", resetpassword);

router.post("/appInfo", appInfoCreate);

//router.post("/doSendEmail", doSendEmail)

module.exports = router;

function authenticateSchema(req, res, next) {
  const schema = Joi.object({
    email: Joi.string().required(),
    password: Joi.string().required(),
    type: Joi.string().optional(),
  });
  validateRequest(req, next, schema);
}

/*function doSendEmail() {
    testEmail();
    // sendEmail("<EMAIL>")
}*/

function authenticate(req, res, next) {
  userService
    .authenticate(req.body)
    .then((user) => res.json({ status: true, result: user }))
    .catch(next);
}

function registerSchema(req, res, next) {
  const schema = Joi.object({
    // firstname: Joi.string().required(),
    // middlename: Joi.string().required(),
    // lastname: Joi.string().allow("",null),
    certificate_status: Joi.string().required(),
    qualification: Joi.string().allow(null, ""),
    email: Joi.string().required(),
    mobile_number: Joi.string().min(10).required(),
    password: Joi.string().min(6).empty(""),
    // date_of_birth: Joi.date().required(),
    name_state_board: Joi.string().allow(null, ""),
    system_of_medicine: Joi.string().allow(null, ""),
    awarding_body: Joi.string().allow(null, ""),
    state_registration_number: Joi.string().allow(null, ""),
    state_registration_date: Joi.date().allow(null, ""),
    old_central_registration_number: Joi.number().allow(null),
    contact_address: Joi.string().required(),
    // permanent_address: Joi.string().required(),
    status: Joi.string().allow(null, ""),
    category: Joi.string().allow(null, ""),
    declaration: Joi.string().allow(null, ""),
    state_id: Joi.number().allow(null),
    district_id: Joi.number().allow(null),
  });
  validateRequest(req, next, schema);
}

function register(req, res, next) {
  userService
    .create(req.body)
    .then((record) =>
      res.json({ message: "Registration successful", userId: record.id })
    )
    .catch(next);
}

function doFileUpload(req, res, next) {
  userService
    .doUpload(req)
    .then(() =>
      res.json({ status: true, message: "File Uploaded Successfully!" })
    )
    .catch(next);
}

function create(req, res, next) {
  userService
    .create(req.body)
    .then((record) =>
      res.json({
        status: true,
        message: "User created successfully",
        userId: record.id,
      })
    )
    .catch(next);
}

function appInfoCreate(req, res, next) {
  userService
    .appInfoDoCreate(req.body)
    .then(() =>
      res.json({
        status: true,
        message: "Record stored successfully",
      })
    )
    .catch(next);
}

function verifyQR(req, res, next) {
  userService
    .doVerifyQR(req.query)
    .then((user) => res.json(user))
    .catch(next);
}

function getAll(req, res, next) {
  userService
    .getAll(req.query)
    .then((users) => res.json(users))
    .catch(next);
}

function getTeamLeaders(req, res, next) {
  userService
    .getTeamLeaders(req.query)
    .then((users) => res.json(users))
    .catch(next);
}

function getDirectors(req, res, next) {
  userService
    .fetchAllDirectors()
    .then((users) => res.json(users))
    .catch(next);
}

function getCurrent(req, res, next) {
  res.json(req.user);
}

function getById(req, res, next) {
  userService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    firstname: Joi.string().empty(""),
    middlename: Joi.string().empty(""),
    lastname: Joi.string().empty(""),
    aadhar_number: Joi.string().min(12).max(12).empty(""),
    email: Joi.string().empty(""),
    mobile_number: Joi.string().required(),
    street1: Joi.string().required(),
    street2: Joi.string().required(),
    city: Joi.string().required(),
    state: Joi.string().required(),
    postcode: Joi.string().required(),
    password: Joi.string().min(6).empty(""),
    status: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  userService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}

function validateEmail(req, res, next) {
  userService
    .validateEmail(req.query.email)
    .then(() => res.json({ status: true }))
    .catch(next);
}

function requestPasswordReset(req, res, next) {
  userService
    .requestPasswordReset(req.body)
    .then(() =>
      res.json({
        status: true,
        message: "Reset password email sent successfully",
      })
    )
    .catch(next);
}

function doPasswordReset(req, res, next) {
  userService
    .doPasswordReset(req.body)
    .then(() =>
      res.json({ status: true, message: "Password changed successfully" })
    )
    .catch(next);
}
function resetpassword(req, res, next) {
  userService
    .resetpassword(req.body)
    .then(() =>
      res.json({ status: true, message: "Password changed successfully" })
    )
    .catch(next);
}
