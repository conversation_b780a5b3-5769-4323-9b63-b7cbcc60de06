﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const readXlsxFile = require("read-excel-file/node");
const { Op } = require("sequelize");
const {
  TEAM_LEADER_ROLE,
  DIRECTOR_ROLE,
  ADMIN_ROLE,
  CEO_ROLE,
} = require("../constants");

// db.InspectionImage.belongsTo(db.Inspection, {
//     as: "inspectionInfo",
//     through: "inspection_images",
//     foreignKey: "inspection_id",
//     otherKey: "inspection_id",
//   });

  db.Inspection.belongsTo(db.InspectionImage, {
    through: "inspection_images",
    foreignKey: "id",
    otherKey: "inspectionId",
    as: "inspectionInfo",
});

  

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  doUpload,
};

async function getAll(params) {
  
  return await db.Inspection.findAll({
    include: [
      {
        model: db.InspectionImage,
         as: "inspectionInfo",
        attributes: ["id", "imageUri"],
      },
    ],
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  params.slug = utils.generateSlug(params.title);
  if (await db.Inspection.findOne({ where: { vinNumber: params.vinNumber } })) {
    throw 'Record "' + params.title + '" is already taken';
    return;
  }

  const record = await db.Inspection.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  params.slug = utils.generateSlug(params.title);
  // validate

  const recordChanged = params.slug && record.slug !== params.slug;
  if (
    recordChanged &&
    (await db.Inspection.findOne({ where: { slug: params.slug } }))
  ) {
    throw 'Role "' + params.title + '" is already taken';
  }

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  let record = await db.Inspection.findByPk(id);
  if (!record) throw "Record not found";
  const inspectionImages = await db.InspectionImage.findAll({where: {
    inspection_id: id
  }})
  let newRecord = {...record?.dataValues};
  console.log("inspectionImages",inspectionImages);
  newRecord.images = inspectionImages;
  return newRecord;
}

// customer bulk create
async function bulkCreateImages(
  rows,
  inspection_id,
  vinNumber
) {
  let inspectionImageList = [];
  let checkPointListNames = ["Bonnet Inspection","Tailgate Inspection", "LHS Door Inspection","RHS Door Inspection"]
  rows.forEach((row, index) => {
    let inspectonImageRecord = {
      inspection_id,
      vinNumber,
      imageUri: row['path'],
      imageName: row['filename'],
      checkPoint: checkPointListNames[index]
    };

    inspectionImageList.push(inspectonImageRecord);
  });
  if (inspectionImageList.length > 0) {
    await db.InspectionImage.bulkCreate(inspectionImageList);
  }
}

async function doUpload(req) {
  try {
    console.log("reqbody",req.body);
    console.log("req.files",req.files);
    console.log("req.checkPointInfo",req?.checkPointInfo);
    let path = __basedir + "images";
    const params = {
      vinNumber: req.body.vinNumber,
      inspectionDate: req?.body?.inspectionDate,
      employeeNumber: req?.body?.employeeNumber,
      inspectorName: req?.body?.inspectorName,
      shift: req?.body?.shift,
      model: req?.body?.model,
    };
    let fileList = req.files;
    const result = await db.Inspection.create(params);
    if (result.id && fileList && fileList.length > 0) {
      await bulkCreateImages(
        fileList,
        result.id,
        params.vinNumber
      );
    }
  } catch (error) {
    if (req.files == undefined) {
      throw "Please upload an excel file!";
      //   return res.status(400).send("Please upload an excel file!");
    } else {
      console.log(error);
      throw "Could not upload the file: ";
      res.status(500).send({
        message: "Could not upload the file: ",
      });
    }
  }
}
