﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
	getAll,
	getById,
	create,
	update,
	delete: _delete,
    getVacancyDocuments
};

// db.Vacancy.belongsTo(db.Advertisement, {
//     through: "vacancies",
//     foreignKey: "advertisement_id",
//     otherKey: "advertisementId",
// });

// db.VacancyDocumentList.belongsTo(db.DocumentList, {
//     through: "documentlists",
//     foreignKey: "documentlist_id",
//     otherKey: "documentlistId",
// });


async function getAll(params) {
    let where =  {
        status: "Active"
    }
    if(!!params?.advertisementId) {
        where = { advertisement_id: params?.advertisementId}
    }
	return await db.Vacancy.findAll({
        where,
        include: [
            {
                model: db.Advertisement,
                attributes: ["id", "advertisement_number"],
            },
        ],
    }
);
}


async function getVacancyDocuments(params) {
    
	return await db.VacancyDocumentList.findAll({
        where: { vacancy_id: params?.vacancy_id},
        include: [
            {
                model: db.DocumentList,
                attributes: ["name"],
            },
        ],
    });
}

async function getById(id) {
	return await getSingleRecord(id);
}

async function create(params) {
	// validate
	// if (await db.Vacancy.findOne({ where: { advertisement_number: params.advertisement_number} })) {
    //     throw 'Record "' + params.advertisement_number + '" is already present';
    //     return;
    // }
    if(Number(params.payscale_from) > Number(params.payscale_to)) {
        throw 'Payscale From cannot be greater than Payscale To';
        return;
    }
    if(Number(params.lower_age) > Number(params.upper_age)) {
        throw 'Lower Age cannot be greater than Upper Age';
        return;
    }
	const record = await db.Vacancy.create(params);
    console.log("record",record);
    if(params.vacancy_documents && params.vacancy_documents.length > 0) {
        // await _deleteDocuments(id);
        await insertVacancyDocuments(record.id, params);
    }
	return record;
	
}

async function update(id, params) {
	const record = await getSingleRecord(id);
	// validate
	
	if(Number(params.payscale_from) > Number(params.payscale_to)) {
        throw 'Payscale From cannot be greater than Payscale To';
        return;
    }
    if(Number(params.lower_age) > Number(params.upper_age)) {
        throw 'Lower Age cannot be greater than Upper Age';
        return;
    }
    
	// copy params to user and save
	Object.assign(record, params);
	await record.save();

    if(params.vacancy_documents && params.vacancy_documents.length > 0) {
        await _deleteDocuments(id);
        await insertVacancyDocuments(id, params);
    } else {
        await _deleteDocuments(id);
    }

	return record.get();
}

async function insertVacancyDocuments(id, params) {
    let vacancyDocuments = [];
    params.vacancy_documents.forEach((value)=>{
        vacancyDocuments.push({
            vacancy_id: id,
            documentlist_id: value
        })
    });
    await db.VacancyDocumentList.bulkCreate(vacancyDocuments);
}

async function _delete(id) {
	const record = await getSingleRecord(id);
	await record.destroy();
}

async function _deleteDocuments(id) {
    return await db.VacancyDocumentList.destroy({where: {
        vacancy_id: id
    }});
}

// helper functions

async function getSingleRecord(id) {
	const record = await db.Vacancy.findByPk(id);
	if (!record) throw "Record not found";
	return record;
}
