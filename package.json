{"name": "u4u", "version": "1.0.0", "description": "u4u", "license": "MIT", "main": "app.js", "scripts": {"start": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 nodemon ./app.js", "start:dev": "nodemon ./app.js"}, "dependencies": {"aws-sdk": "^2.1046.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "ejs": "^3.1.9", "express": "^4.17.1", "express-jwt": "^6.0.0", "joi": "^17.2.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.4", "multer": "^1.4.4", "multer-s3": "^2.10.0", "mysql2": "^2.1.0", "node-forge": "^1.3.1", "nodemailer": "^6.7.7", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "razorpay": "^2.9.2", "read-excel-file": "^5.5.3", "rootpath": "^0.1.2", "sequelize": "^6.3.4", "uuidv4": "^6.2.12"}, "devDependencies": {"nodemon": "^2.0.20"}}