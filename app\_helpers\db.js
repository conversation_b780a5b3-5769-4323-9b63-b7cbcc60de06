const config = require("../../config.json");
const mysql = require("mysql2/promise");
const { Sequelize } = require("sequelize");

module.exports = db = {};

initialize();

async function initialize() {
    // create db if it doesn't already exist
    const { host, port, user, password, database } = config.database;
    // connect to dbs
    const sequelize = new Sequelize(database, user, password, {
        host: host,
        port: port,
        dialect: "mysql",
        pool: {
            max: 100,
            min: 0,
            idle: 5000,
            evict: 10000,
        }
    });

    sequelize.authenticate().then(() => {
        console.log('Connection has been established successfully.');
     }).catch((error) => {
        console.error('Unable to connect to the database: ', error);
     });

    // init models and add them to the exported db object
    // db.Inspection = require("../inspections/inspection.model")(sequelize);
    db.User = require("../users/user.model")(sequelize);
    db.PortalUser = require("../portalusers/portaluser.model")(sequelize);
    db.Role = require("../roles/role.model")(sequelize);
    // db.Advertisement = require("../advertisements/advertisement.model")(sequelize);
    // db.Category = require("../categories/category.model")(sequelize);
    // db.Vacancy = require("../vacancies/vacancy.model")(sequelize);
    // db.Application = require("../applications/application.model")(sequelize);
    db.Profile = require("../myprofile/myprofile.model")(sequelize);
    db.Setting = require("../settings/setting.model")(sequelize);
    // db.Experience = require("../experiences/experience.model")(sequelize);
    db.Document = require("../documents/document.model")(sequelize);
    db.DocumentList = require("../documentlists/documentlist.model")(sequelize);
    db.Payment = require("../payments/payment.model")(sequelize);
    // db.VacancyDocumentList = require("../vacancies/vacancy_documentlist/vacancy_documentlist.model")(sequelize);


    // sync all models with database
    await sequelize.sync();

    db.sequelize = sequelize;
}
