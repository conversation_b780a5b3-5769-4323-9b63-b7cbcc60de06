﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const documentlistService = require("./documentlist.service");

// routes
router.get("/", getAll);
router.post("/", authorize(), updateSchema, create);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), updateSchema, update);
router.delete("/:id", authorize(), _delete);

module.exports = router;

function create(req, res, next) {
	documentlistService
        .create(req.body)
        .then(() =>
            res.json({ status: true, message: "Record created successfully" })
        )
        .catch(next);
}

function getAll(req, res, next) {
	documentlistService
		.getAll(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function getById(req, res, next) {
	documentlistService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		name: Joi.string().required(),
		size: Joi.string().required(),
		filetype: Joi.string().required(),
		status: Joi.string().required(),
        is_mandatory: Joi.string().required(),
        additionalFieldName: Joi.string().allow("",null),
        certificate_status: Joi.string().required(),
        description: Joi.string().allow("",null),
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	documentlistService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function _delete(req, res, next) {
	documentlistService
		.delete(req.params.id)
		.then(() => res.json({ status: true, message: "Record deleted successfully" }))
		.catch(next);
}
