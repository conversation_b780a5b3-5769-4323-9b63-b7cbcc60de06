const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        user_id: { type: DataTypes.INTEGER, allowNull: false },
        advertisement_id: { type: DataTypes.INTEGER, allowNull: false },
        category_name: { type: DataTypes.STRING, allowNull: false },
        vacancy_id: { type: DataTypes.INTEGER, allowNull: false },
        applied_on: { type: DataTypes.DATE, allowNull: false },
        fees_paid_on: { type: DataTypes.DATE, allowNull: true },
        form_submitted_on: { type: DataTypes.DATE, allowNull: false },
        status: { type: DataTypes.STRING, allowNull: false },
        remarks: { type: DataTypes.STRING, allowNull: true },
        updated_by: { type: DataTypes.INTEGER, allowNull: true },
    };

    const options = {
        defaultScope: {
            // exclude hash by default
        },
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("applications", attributes, options);
}
