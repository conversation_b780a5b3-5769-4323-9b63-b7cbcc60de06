﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");
const moment = require("moment/moment");

module.exports = {
	getById,
	update,
	delete: _delete,
};


async function getById(id) {
	return await getUser(id);
}


async function update(id, params) {
	const record = await getSingleRecord(id);
	// validate
	const emailChanged = params.email && record.email !== params.email;
	if (
		emailChanged &&
		(await db.Profile.findOne({ where: { email: params.email } }))
	) {
		throw 'email "' + params.email + '" is already taken';
	}

	// hash password if it was entered
	if (params.password) {
		params.password = await bcrypt.hash(params.password, 10);
	}
    if(!!params?.date_of_birth) {
        params.date_of_birth = moment(params.date_of_birth).format("YYYY-MM-DD")
    }
    if(!!params?.state_registration_date) {
        params.state_registration_date = moment(params.state_registration_date).format("YYYY-MM-DD")
    } else {
        params.state_registration_date = null;   
    }
    if(params?.profileImagePath) {
        params.profileImageUploaded = "25";
    }
	// copy params to user and save
	Object.assign(record, params);
	await record.save();

	return omitHash(record.get());
}

async function _delete(id) {
	const user = await getUser(id);
	await user.destroy();
}

async function getSingleRecord(id) {
    const record = await db.Profile.findOne({
        where: {
            id: id,
        },
    });
    if (!record) throw "Invalid ID";
    return record;
}

// helper functions

async function getUser(id) {
	const user = await db.Profile.findByPk(id);
	if (!user) throw "User not found";
	return user;
}

function omitHash(user) {
	const { password, ...userWithoutHash } = user;
	return userWithoutHash;
}

