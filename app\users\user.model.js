// import  DataTypes from "sequelize";
const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    firstname: { type: DataTypes.STRING, allowNull: true },
    middlename: { type: DataTypes.STRING, allowNull: true },
    lastname: { type: DataTypes.STRING, allowNull: true },
    mobile_number: { type: DataTypes.STRING, allowNull: false },
    email: { type: DataTypes.STRING, allowNull: false },
    password: { type: DataTypes.STRING, allowNull: false },
    gender: { type: DataTypes.STRING, allowNull: true },
    aadharPhotoUrl: { type: DataTypes.STRING, allowNull: true },
    date_of_birth: { type: DataTypes.DATE, allowNull: true },
    name_state_board: { type: DataTypes.STRING, allowNull: true },
    system_of_medicine: { type: DataTypes.STRING, allowNull: false },
    awarding_body: { type: DataTypes.STRING, allowNull: false },
    state_registration_number: { type: DataTypes.STRING, allowNull: true },
    state_registration_date: { type: DataTypes.DATE, allowNull: true },
    central_registration_number: { type: DataTypes.STRING, allowNull: true },
    old_central_registration_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    contact_address: { type: DataTypes.STRING, allowNull: true },
    permanent_address: { type: DataTypes.STRING, allowNull: true },
    status: { type: DataTypes.STRING, allowNull: false },
    token: { type: DataTypes.STRING, allowNull: true },
    certificate_status: { type: DataTypes.STRING, allowNull: true },
    qualification: { type: DataTypes.STRING, allowNull: true },
    profileImagePath: { type: DataTypes.STRING, allowNull: true },
    documentUploaded: { type: DataTypes.INTEGER, allowNull: true },
    profileImageUploaded: { type: DataTypes.INTEGER, allowNull: true },
    signatureImagePath: { type: DataTypes.STRING, allowNull: true },
    profile_completed: { type: DataTypes.INTEGER, allowNull: true },
    profile_status: { type: DataTypes.STRING, allowNull: true },
    payment_id: { type: DataTypes.INTEGER, allowNull: true },
    approved_date: { type: DataTypes.DATE, allowNull: true },
    approved_by: { type: DataTypes.INTEGER, allowNull: true },
    remarks: { type: DataTypes.STRING, allowNull: true },
    category: { type: DataTypes.STRING, allowNull: true },
    declaration: { type: DataTypes.STRING, allowNull: true },
    central_registration_number_pre: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    aadharVerified: { type: DataTypes.STRING, allowNull: true },
    accountExistResponse: { type: DataTypes.TEXT, allowNull: true },
    mobileandAadharMobileResponse: { type: DataTypes.TEXT, allowNull: true },
    usersData: { type: DataTypes.TEXT, allowNull: true },
    userName: { type: DataTypes.STRING, allowNull: true },
    textPassword: { type: DataTypes.STRING, allowNull: true },
    district_id: { type: DataTypes.INTEGER, allowNull: true },
    state_id: { type: DataTypes.INTEGER, allowNull: true },
    createHPRIDResponse: { type: DataTypes.TEXT, allowNull: true },

    healthProfessionalType: { type: DataTypes.STRING, allowNull: true },
    salutation: { type: DataTypes.STRING, allowNull: true },
    languagesSpoken: { type: DataTypes.STRING, allowNull: true },

    pincode: { type: DataTypes.STRING, allowNull: true },
    registeredWithCouncil: { type: DataTypes.INTEGER, allowNull: true },
    registrationNumber: { type: DataTypes.STRING, allowNull: true },
    isPermanentOrRenewable: { type: DataTypes.STRING, allowNull: true },
    renewableDueDate: { type: DataTypes.STRING, allowNull: true },
    nameOfDegreeOrDiplomaObtained: { type: DataTypes.INTEGER, allowNull: true },
    college: { type: DataTypes.INTEGER, allowNull: true },
    university: { type: DataTypes.INTEGER, allowNull: true },
    yearOfAwardingDegreeDiploma: { type: DataTypes.STRING, allowNull: true },
    currentlyWorking: { type: DataTypes.INTEGER, allowNull: true },
    chooseWorkStatus: { type: DataTypes.INTEGER, allowNull: true },
    reasonForNotWorking: { type: DataTypes.STRING, allowNull: true },
    authPasswordResp: { type: DataTypes.TEXT, allowNull: true },
    registerProfessionalResponse: { type: DataTypes.TEXT, allowNull: true },
    didResetPassword: { type: DataTypes.STRING, allowNull: true },
    registrationDate: { type: DataTypes.DATE, allowNull: true },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
      attributes: { exclude: ["password"] },
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("users", attributes, options);
}

// function model(sequelize) {

// }
