const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        user_id: { type: DataTypes.INTEGER, allowNull: false },
        name_of_organization: { type: DataTypes.STRING, allowNull: false },
        post: { type: DataTypes.STRING, allowNull: false },
        start_date: { type: DataTypes.DATE, allowNull: false },
        end_date: { type: DataTypes.DATE, allowNull: false },
        pay_scale: { type: DataTypes.STRING, allowNull: false },
        nature_of_duties: { type: DataTypes.STRING, allowNull: false },
        fileName: { type: DataTypes.STRING, allowNull: true },
    };

    const options = {
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("experiences", attributes, options);
}
