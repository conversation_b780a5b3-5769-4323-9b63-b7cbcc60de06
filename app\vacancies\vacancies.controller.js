﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const vacancyService = require("./vacancy.service");

// routes
router.get("/", getAll);
router.post("/", authorize(), updateSchema, create);
router.get("/getVacancyDocuments", authorize(), getVacancyDocuments);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), updateSchema, update);
router.delete("/:id", authorize(), _delete);

module.exports = router;

function create(req, res, next) {
	vacancyService
        .create(req.body)
        .then(() =>
            res.json({ status: true, message: "Record created successfully" })
        )
        .catch(next);
}

function getAll(req, res, next) {
	vacancyService
		.getAll(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function getVacancyDocuments(req, res, next) {
	vacancyService
		.getVacancyDocuments(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function getById(req, res, next) {
	vacancyService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		advertisement_id: Joi.string().required(),
		department_name: Joi.string().required(),
		vacancy_name: Joi.string().required(),
		nature: Joi.string().required(),
		payscale_from: Joi.string().required(),
		payscale_to: Joi.string().required(),
		lower_age: Joi.string().required(),
		upper_age: Joi.string().required(),
		total_no_of_vacancy: Joi.string().required(),
		createdBy: Joi.string().required(),
		updatedBy: Joi.string().allow('',null),
		status: Joi.string().required(),
        vacancy_documents: Joi.array().required()
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	vacancyService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function _delete(req, res, next) {
	vacancyService
		.delete(req.params.id)
		.then(() => res.json({ status: true, message: "Record deleted successfully" }))
		.catch(next);
}
