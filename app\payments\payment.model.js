const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        payment_id: {
            type: DataTypes.INTEGER,
            primaryKey: true
        },
        order_id: { type: DataTypes.STRING, allowNull: false },
        user_id: { type: DataTypes.INTEGER, allowNull: false },
        amount: { type: DataTypes.BIGINT, allowNull: false },
        entity: { type: DataTypes.STRING, allowNull: true },
        receipt: { type: DataTypes.STRING, allowNull: true },
        status: { type: DataTypes.STRING, allowNull: true },
        razorpay_payment_id: { type: DataTypes.STRING, allowNull: true },
        razorpay_signature: { type: DataTypes.STRING, allowNull: true },
        payment_status: { type: DataTypes.STRING, allowNull: true },
        tracking_id:{ type: DataTypes.STRING, allowNull: true },
        payment_mode: { type: DataTypes.STRING, allowNull: true },
        bank_ref_no:{ type: DataTypes.STRING, allowNull: true },
        all_values:{ type: DataTypes.TEXT, allowNull: true },
        payment_type:{ type: DataTypes.STRING, allowNull: true },
        original_payment_id:{ type: DataTypes.INTEGER, allowNull: true }
      };

    const options = {
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("payments", attributes, options);
}
