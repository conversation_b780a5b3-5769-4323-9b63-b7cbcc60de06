const nodemailer = require("nodemailer");
const config = require("../../config.json");


const sendEmail = async (email, subject, emailBody) => {
    const { host, port, user, password } = config.emailSettings;
	return new Promise((resolve,reject)=>{

	let transporter = nodemailer.createTransport({
		host: host,
		port: port,
		secure: true, // true for 465, false for other ports
		auth: {
			user: user, // generated ethereal user
			pass: password, // generated ethereal password
		},
	});

	const emailOptions = {
		from: user, // sender address
		to: email, // list of receivers
		subject: subject, // Subject line
		// text: "Reset Password Text", // plain text body
		html: emailBody, // html body
	};
    console.log("inse mail");
	transporter.sendMail(emailOptions, function(error, info){
		if (error) {
			console.log("error is "+error);
		   resolve(false); // or use rejcet(false) but then you will have to handle errors
		} 
	   else {
		   console.log('Email sent: ' + info.response);
		   resolve(true);
		}
	   });
	 })  

};

const generateRandowmPassword = () => {
    var length = 8,
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
        retVal = "";
    for (var i = 0, n = charset.length; i < length; ++i) {
        retVal += charset.charAt(Math.floor(Math.random() * n));
    }
    return retVal;
}

const generateSlug = (name) => {
    return name.toLowerCase().trim().split(" ").join("-");
}


module.exports = {
	sendEmail,
	generateRandowmPassword,
	generateSlug
}