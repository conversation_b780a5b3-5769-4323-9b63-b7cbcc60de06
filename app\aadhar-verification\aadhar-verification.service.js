const crypto = require("crypto");

const encryptValue = (value, publicKey) => {
  try {


    const encrypted = crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING,
      },
      Buffer.from(value)
    );

    return encrypted.toString("base64");
  } catch (error) {
    console.log(error, "what erro");
    throw new Error("Encryption failed");
  }
};

module.exports = {
  encryptValue,
};
