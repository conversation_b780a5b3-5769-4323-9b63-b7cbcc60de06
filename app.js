require("rootpath")();
const express = require("express");

const app = express();
const cors = require("cors");
const bodyParser = require("body-parser");
const errorHandler = require("./app/_middleware/error-handler");
global.__basedir = __dirname + "/";
const DIR = "./images";
const PDFDIR_QUALIFICATION = "./uploads/qualifications/";
const PDFDIR_EXPERIENCES = "./uploads/experiences/";
const PDFDIR_DOCUMENTS = "./uploads/documents/";

const PDFDIR_ADVERTISEMENTS = "./uploads/";
app.engine("html", require("ejs").renderFile);

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(cors());

// api routes
app.get("/api/", (req, res) => {
  res.json({ message: "Welcome to application." });
});
app.use("/api/users", require("./app/users/users.controller"));
app.use("/api/roles", require("./app/roles/roles.controller"));
app.use(
  "/api/advertisements",
  require("./app/advertisements/advertisements.controller")
);
app.use("/api/vacancies", require("./app/vacancies/vacancies.controller"));
app.use(
  "/api/applications",
  require("./app/applications/applications.controller")
);
app.use(
  "/api/portalusers",
  require("./app/portalusers/portalusers.controller")
);
app.use("/api/profile", require("./app/myprofile/myprofile.controller"));
app.use("/api/settings", require("./app/settings/settings.controller"));
app.use(
  "/api/experiences",
  require("./app/experiences/experiences.controller")
);
app.use("/api/documents", require("./app/documents/documents.controller"));
app.use(
  "/api/documentlists",
  require("./app/documentlists/documentlists.controller")
);
app.use("/api/payments", require("./app/payments/payments.controller"));
app.use(
  "/api/aadhar-verfication",
  require("./app/aadhar-verification/aadhar-verification.controller")
);
app.use(
  "/api/personal-information",
  require("./app/professional-information/professional-information.controller")
);
// app.use("/blogs", require("./app/blogs/blogs.controller"));
// app.use(express.static(DIR));

// app.use(express.static(__dirname + '/resources/static/uploads'));

app.use("/api/fetchImages", express.static(DIR));
app.use("/api/fetchQualificationPDF", express.static(PDFDIR_QUALIFICATION));
app.use("/api/fetchExperiencesPDF", express.static(PDFDIR_EXPERIENCES));
app.use("/api/fetchDocumentsPDF", express.static(PDFDIR_DOCUMENTS));
app.use("/api/fetchAdvertisementsPDF", express.static(PDFDIR_ADVERTISEMENTS));

// global error handler
app.use(errorHandler);

// start server
const port =
  process.env.NODE_ENV === "production" ? process.env.PORT || 80 : 4000;
app.listen(port, () => console.log("Server listening on port " + port));
