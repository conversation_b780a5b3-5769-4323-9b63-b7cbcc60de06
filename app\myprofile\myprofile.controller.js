const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const multer = require("multer");

const userService = require("./myprofile.service");

const DIR = "./images/";

const storage = multer.diskStorage({
	destination: (req, file, cb) => {
		cb(null, DIR);
	},
	filename: (req, file, cb) => {
        
		const fileName = file.originalname.toLowerCase().split(" ").join("-");
		cb(null, fileName);
	},
});

// STAND ALONE CONFIG
const uploadConfig = multer({
	storage: storage,
	fileFilter: (req, file, cb) => {
		if (
			file.mimetype == "image/png" ||
			file.mimetype == "image/jpg" ||
			file.mimetype == "image/jpeg"
		) {
			cb(null, true);
		} else {
			cb(null, false);
			return cb(new Error("Only .png, .jpg and .jpeg format allowed!"));
		}
	},
});

// routes
router.get("/verifyQR/:id", verifyQR);
router.get("/:id",  getById);
router.put(
	"/:id",
	uploadConfig.array("images"),
	update
);
//router.post("/doSendEmail", doSendEmail)

module.exports = router;

function getById(req, res, next) {
	userService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}

function verifyQR(req, res, next) {
    // userService.doVerifyQR(req.params).then(())
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		firstName: Joi.string().empty(""),
		lastName: Joi.string().empty(""),
		email: Joi.string().empty(""),
		phoneNumber: Joi.string().required(),
		zipcode: Joi.string().required(),
		city: Joi.string().required(),
		state: Joi.string().required(),
		country: Joi.string().required(),
		address: Joi.string().required(),
		password: Joi.string().min(6).empty(""),
		profileImagePath: Joi.string().allow("",null),
        companyImagePath: Joi.string().allow("",null),
        images: Joi.string().allow("",null),
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	userService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}
