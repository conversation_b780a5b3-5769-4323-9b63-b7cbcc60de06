const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
    const attributes = {
        advertisement_id: { type: DataTypes.INTEGER, allowNull: false },
        department_name: { type: DataTypes.STRING, allowNull: false },
        vacancy_name: { type: DataTypes.STRING, allowNull: true },
        nature: { type: DataTypes.STRING, allowNull: false },
        payscale_from: { type: DataTypes.STRING, allowNull: false },
        payscale_to: { type: DataTypes.STRING, allowNull: false },
        lower_age: { type: DataTypes.STRING, allowNull: true },
        upper_age: { type: DataTypes.STRING, allowNull: false },
        total_no_of_vacancy: { type: DataTypes.BIGINT, allowNull: false },
        createdBy: { type: DataTypes.INTEGER, allowNull: false },
        updatedBy: { type: DataTypes.INTEGER, allowNull: true },
        status: { type: DataTypes.STRING, allowNull: false },
    };

    const options = {
        defaultScope: {
            // exclude hash by default
        },
        scopes: {
            // include hash with this scope
            withHash: { attributes: {} },
        },
    };

    return sequelize.define("vacancies", attributes, options);
}
