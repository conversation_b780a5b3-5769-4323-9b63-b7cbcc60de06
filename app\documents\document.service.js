﻿const config = require("../../config.json");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const db = require("../_helpers/db");

module.exports = {
  create,
  getById,
  update,
  delete: _delete,
  getAll,
};

async function create(params) {
  // validate
  const record = await db.Document.create(params);
  if (record) {
    // copy params to user and save
    const profileRecord = await db.Profile.findOne({
      where: {
        id: params?.user_id,
      },
    });

    Object.assign(profileRecord, { documentUploaded: 25 });
    await profileRecord.save();
  }
  return record;
  // return true;
}

async function getAll(params) {
  const where = {};
  if (params?.user_id) {
    where.user_id = params?.user_id;
  }
  if (params?.documentName) {
    where.documentName = params?.documentName;
  }
  return await db.Document.findAll({
    where: where,
  });
}

async function getById(id) {
  return await getDocument(id);
}

async function update(id, params) {
  const record = await getSingleRecord(id);
  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getDocument(id);
  await record.destroy();
}

async function getSingleRecord(id) {
  const record = await db.Document.findOne({
    where: {
      id: id,
    },
  });
  if (!record) throw "Invalid ID";
  return record;
}

// helper functions

async function getDocument(id) {
  const document = await db.Document.findByPk(id);
  if (!document) throw "Record not found";
  return document;
}
