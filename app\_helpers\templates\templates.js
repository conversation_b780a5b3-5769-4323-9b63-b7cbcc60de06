const requestPasswordTemplate = (name, link) => {
	return `<html>
      <head>
        <style></style>
      </head>
      <body>
        <p>Hi ${name},</p>
        <p>We're sending you the email because you requested a password reset. Click on this link to create a new password : <a href="${link}">Reset Password Link</a></p>
        <p> If you didn't request a password reset, you can ignore this email.<br />Your Password will not be changed</p>
        <p>Thanks<br />
        The MyHouser Team</p>
      </body>
    </html>`;
};

const newAccountCreationTemplate = (firstName, lastName, email, password, link, userType) => {
	return `<html>
   <head>
     <style></style>
   </head>
   <body>
     <p>Dear ${firstName},</p>
     <p>Congratulations on your decision to join the MyHouser community. We all are about making a difference to the home ownership experience. Your decision to join this community comes out of caring and putting your clients interest front and forward.</p>
     <p>You are not just a subscriber, but you are going to be a member of this coveted community that takes care of their clients, work with them on their lifetime homeownership experience and advise them in their best interests.</p>
     <p>We the team at <a href="${link}">Myhouser.com</a> will be continuously and rigorously working on developing the best of the breed calculators, online wealth management tools that will empower the members to visualize, plan and achieve stellar results in building wealth with real estate and a combination of other financial vehicles. Because we are helping our clients in multiple aspects include Family, Finances while enjoying and building a career.</p>
     <p>Welcome to this new experience of ownership by design. Let’s collectively work together to help our clients build their dreams into reality.</p>
     <p>Please use the following credentials to login.</p>
     <p>${link}</p>
     <h3>Email Address: ${email}</h3>
     <h3>Password: ${password}</h3>
     <p>Please change password after login to the application.</p>
     <p>From Myhouser.com team,</p>
     <p>Amol Heda</p>
   </body>
 </html>`;
};

const clarificationPending = (firstName, remarks) => {
	return `<html>
   <head>
     <style></style>
   </head>
   <body>
     <p>Dear ${firstName},</p>
     <p>Your application has been scrutinized and we request you to fill the requested details at the earliest</p>
     <p>Details : ${remarks}</p>
     <p>${link}</p>
     <p>NRB Admin</p>
   </body>
 </html>`;
};

const emailForNewRegistered = (profileStatus, centralNumber, remkars) => {
    return `<html>
   <head>
     <style></style>
   </head>
   <body>
     <p>Dear Applicant,</p>
     <p>Your online application for Central Registration Certificate with Naturopathy Registration Board is successfully completed. Your application no. is ${centralNumber}. The certificate will be sent to your postal address shortly.</p>
     <br />
     <p><strong>Disclaimer: The Central Registration Certificate is printed as per the information submitted by the candidate. Naturopathy Registration Board will not be held responsible for any mistake done by the candidate.</strong> </p>
     <br />
     <p> 
        Thank you
        <div>Team</div>
        <div>Naturopathy Registration Board</div>
    </p>
   </body>
 </html>`;
}

const emailForCCRYNRegistered = (profileStatus, centralNumber, remkars) => {
    return `<html>
   <head>
     <style></style>
   </head>
   <body>
     <p>Dear Applicant,</p>
     <p>Your online application for Central Registration Certificate with Naturopathy Registration Board is successfully completed. Your application no. is  ${centralNumber}. </p>
     <br />
     <p>Please send the original certificate issued by CCRYN to you to the below address:</p>
     <p>NATUROPATHY REGISTRATION BOARD</p>
     <p>(Under the aegis of National Institute of Naturopathy)</p>
     <p>Ministry of Ayush, Government of India</p>
     <p>‘Bapu Bhavan’, Matoshree Ramabai Ambedkar Road, Pune – 411001</p>
     <br />
     <p>Kindly note, the Central Registration certificate will be issued only on receipt of the CCRYN certificate. The certificate will be sent to your registered email id and the postal address.</p>
     <p><strong>Disclaimer: The Central Registration Certificate is printed as per the information submitted by the candidate. Naturopathy Registration Board will not be held responsible for any mistake done by the candidate.</strong> </p>
     <br />
     <p> 
        Thank you
        <div>Team</div>
        <div>Naturopathy Registration Board</div>
    </p>
   </body>
 </html>`;
}

const emailForRejectedApplication = (firstName, centralNumber, remkars) => {
    return `<html>
   <head>
     <style></style>
   </head>
   <body>
     <p>Dear ${firstName},</p>
     <p>We regret to inform you that your application for registration with the Naturopathy Registration Board has been rejected.</p>
     <p>Please note that a processing fee of Rs. 500/- has been deducted from your application fees. The remaining balance of Rs. 2000/- will be refunded to your account shortly.</p><br />
     <p> 
        Thank you
    </p>
   </body>
 </html>`;
}

module.exports = {
	requestPasswordTemplate,
	newAccountCreationTemplate,
    clarificationPending,
    emailForNewRegistered,
    emailForCCRYNRegistered,
    emailForRejectedApplication
};
