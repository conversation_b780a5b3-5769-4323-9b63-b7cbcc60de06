﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const advertisementService = require("./advertisement.service");
const uploadFile = require("../_middleware/upload");

// routes
router.get("/", getAll);
router.post("/", authorize(), create);
router.get("/:id", authorize(), getById);
router.put("/:id", authorize(), updateSchema, update);
router.delete("/:id", authorize(), _delete);
router.post("/upload", uploadFile.array("files"), doFileUpload);


module.exports = router;

function create(req, res, next) {
	advertisementService
        .create(req.body)
        .then(() =>
            res.json({ status: true, message: "Record created successfully" })
        )
        .catch(next);
}

function doFileUpload(req, res, next) {
    advertisementService
      .doUpload(req)
      .then(() =>
        res.json({ status: true, message: "File Uploaded Successfully!" })
      )
      .catch(next);
  }

function getAll(req, res, next) {
	advertisementService
		.getAll(req.query)
		.then((records) => {
			// return Promise.all(records.map(async element => {
			//     element.agentList = await db.Broker.count({ where: { roleId: 3, brokerId: element.id } });
			//     return element;
			// })).then(() => {
			//     res.json(records);
			//     next();
			// });

			res.json(records);
		})
		.catch(next);
}

function getById(req, res, next) {
	advertisementService
		.getById(req.params.id)
		.then((user) => res.json(user))
		.catch(next);
}

function updateSchema(req, res, next) {
	const schema = Joi.object({
		mode_of_recruitment: Joi.string().required(),
		examination_name: Joi.string().required(),
		advertisement_number: Joi.string().required(),
		advertisement_date: Joi.date().required(),
		start_date: Joi.date().required(),
		fee_last_date: Joi.date().required(),
		form_submission_last_date: Joi.date().required(),
		createdBy: Joi.string().required(),
		updatedBy: Joi.string().allow('',null),
		status: Joi.string().required(),
        advertisement_file:Joi.object(),
        user_instructions:Joi.object(),
	});
	validateRequest(req, next, schema);
}

function update(req, res, next) {
	advertisementService
		.update(req.params.id, req.body)
		.then((user) => res.json(user))
		.catch(next);
}

function _delete(req, res, next) {
	advertisementService
		.delete(req.params.id)
		.then(() => res.json({ status: true, message: "Record deleted successfully" }))
		.catch(next);
}
