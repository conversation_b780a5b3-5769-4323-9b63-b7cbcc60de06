﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
	getAll,
	getById,
	create,
	update,
	delete: _delete,
};

async function getAll() {
	return await db.Setting.findAll({where: {
        status: "Active"
    }});
}

async function getById(id) {
	return await getSingleRecord(id);
}

async function create(params) {
	// validate

	const record = await db.Setting.create(params);
	return record;
	
}

async function update(id, params) {
	const record = await getSingleRecord(id);

	// copy params to user and save
	Object.assign(record, params);
	await record.save();

	return record.get();
}

async function _delete(id) {
	const record = await getSingleRecord(id);
	await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
	const record = await db.Setting.findByPk(id);
	if (!record) throw "Record not found";
	return record;
}
