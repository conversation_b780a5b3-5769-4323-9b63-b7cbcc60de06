﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");

const { Op } = require("sequelize");
const { fn, col } = db.User.sequelize;

module.exports = {
    getAll,
    getById,
    create,
    update,
    delete: _delete,
    generateReports,
    generateDashboardRecords,
};

// db.Application.belongsTo(db.Vacancy, {
//     through: "vacancies",
//     foreignKey: "vacancy_id",
//     otherKey: "vacancyId",
// });

// db.Application.belongsTo(db.User, {
//     through: "users",
//     foreignKey: "user_id",
//     otherKey: "userId",
// });

// db.Application.belongsTo(db.Advertisement, {
//     through: "advertisements",
//     foreignKey: "advertisement_id",
//     otherKey: "advertisementId",
// });

async function getAll(params) {
    if (!!params?.user_id) {
        return await db.Application.findAll({
            where: {
                user_id: params?.user_id,
            },
            include: [
                {
                    model: db.Vacancy,
                    attributes: [
                        "id",
                        "vacancy_name",
                        "department_name",
                        "nature",
                    ],
                },
                {
                    model: db.User,
                    attributes: ["id", "firstname", "lastname"],
                },
            ],
        });
    } else {
        console.log('params',params);
        return await db.Application.findAll({
            include: [
                {
                    model: db.Vacancy,
                    attributes: [
                        "id",
                        "vacancy_name",
                        "department_name",
                        "nature",
                    ],
                },
                {
                    model: db.User,
                },
            ],
        });
    }
}

async function generateReports(params) {
    let where = {};
    if (!!params?.advertisement_id && params?.advertisement_id !== "All") {
        where = {
            ...where,
            advertisement_id: params?.advertisement_id,
        };
    }
    if (!!params?.category_name && params?.category_name !== "All") {
        where = {
            ...where,
            category_name: params?.category_name,
        };
    }
    if (!!params?.vacancy_id && params?.vacancy_id !== "All") {
        where = {
            ...where,
            vacancy_id: params?.vacancy_id,
        };
    }
    
    console.log("where",where);

    return await db.Application.findAll({
        where,
        include: [
            {
                model: db.Advertisement,
                attributes: ["id", "advertisement_number"],
            },
            {
                model: db.Vacancy,
                attributes: ["id", "vacancy_name", "department_name", "nature"],
            },
            {
                model: db.User,
                attributes: ["id", "firstname", "lastname","mobile_number","email","city","state","street1"],
            },
        ],
    });
}

async function generateDashboardRecords(params) {
    let where = {};
    const TODAY_START = new Date().setHours(0, 0, 0, 0);
    const NOW = new Date().setHours(23, 59, 59, 0);

    const totalNewApplicantsCount = await db.User.count({where: {
        certificate_status: "New Certificate"
    }});
    const totalRenewalApplicantsCount = await db.User.count({where: {
        certificate_status: "Old Certificate"
    }});
    const approvedNewApplicants = await db.User.count({where: {
        certificate_status: "New Certificate",
        profile_status:"Approved"
    }});
    const approvedRenewalApplicants = await db.User.count({where: {
        certificate_status: "Old Certificate",
        profile_status:"Approved"
    }});
    const rejectedApplicants = await db.User.count({where: {
        profile_status:"Rejected"
    }});
    const paymentsCount = await db.Payment.count({where: {
        payment_status:"success"
    }});
    // const todaysApplicationList = await db.Application.findAll({
    //     where: {
    //         createdAt: {
    //             [Op.gt]: TODAY_START,
    //             [Op.lt]: NOW,
    //         },
    //     },
    //     include: [
    //         {
    //             model: db.Advertisement,
    //             attributes: ["id", "advertisement_number"],
    //         },
    //         {
    //             model: db.Vacancy,
    //             attributes: ["id", "vacancy_name", "department_name", "nature"],
    //         },
    //         {
    //             model: db.User,
    //         },
    //     ],
    // });
    // const todaysApplicationCount = todaysApplicationList?.length;
    const totalMembers = await db.User.count({
        where: {
            status: "Active",
        },
    });

    return {
        overallStatus: {
            totalNewApplicantsCount,
            totalRenewalApplicantsCount,
            totalMembers,
            approvedRenewalApplicants,
            approvedNewApplicants,
            rejectedApplicants,
            paymentsCount
        },
        gridData: [],
    };
}

async function getById(id) {
    return await getSingleRecord(id);
}

async function create(params) {
    // validate
    if (
        await db.Application.findOne({
            where: { vacancy_id: params.vacancy_id, user_id: params.user_id },
        })
    ) {
        throw "You have already applied for this Vacancy";
        return;
    }

    const record = await db.Application.create(params);
    return record;
}

async function update(id, params) {
    const record = await getSingleRecord(id);
    // validate

    const recordChanged =
        params.vacancy_id && record.vacancy_id !== params.vacancy_id;
    if (
        recordChanged &&
        (await db.Application.findOne({
            where: { vacancy_id: params.vacancy_id, user_id: params.user_id },
        }))
    ) {
        throw "You have already applied for this Vacancy";
        return;
    }

    // copy params to user and save
    Object.assign(record, params);
    await record.save();

    return record.get();
}

async function _delete(id) {
    const record = await getSingleRecord(id);
    await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
    const record = await db.Application.findByPk(id);
    if (!record) throw "Record not found";
    return record;
}
